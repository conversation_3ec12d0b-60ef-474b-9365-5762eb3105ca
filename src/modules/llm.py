import os
from abc import ABC, abstractmethod
from typing import Dict, List

import logging

from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()
logger = logging.getLogger(__name__)

TEMPERATURE=0.0
MAX_TOKENS=4096

class LLMBase(ABC):

    @abstractmethod
    async def acompletion(self, messages: List[Dict], **kwargs) -> str:
        """Abstract method for calling the provider's API."""
        pass

    @abstractmethod
    async def acompletion_tools(self, messages: List[Dict], tools: List[Dict], **kwargs) -> List[Dict]:
        """Abstract method for calling the provider's API with tools."""
        pass


class LLM(LLMBase):
    def __init__(self, llm_config: Dict):
        super().__init__()
        self.llm_config = llm_config
        self.client = OpenAI(api_key=llm_config.get("api_key"), base_url=llm_config.get("base_url"))

    def _construct_messages_with_history(self, system_prompt: str, message: str, context: Dict, extra_instructions: str = "") -> List[Dict]:
        history = context.get("history", [])
        messages = [{"role": "system", "content": system_prompt}]
        for entry in history:
            messages.append({"role": "user", "content": entry["message"]})
            messages.append({"role": "assistant", "content": entry["response"]})
        user_content = f"{message}\n\n{extra_instructions}" if extra_instructions else message
        messages.append({"role": "user", "content": user_content})
        return messages

    async def acompletion(self, messages: List[Dict], **kwargs) -> str:
        response = self.client.chat.completions.create(
            model=self.llm_config.get("model_name"),
            messages=messages,
            **kwargs
        )
        return response.choices[0].message.content.strip()

    async def acompletion_tools(self, messages: List[Dict], tools: List[Dict], **kwargs) -> List[Dict]:
            response = self.client.chat.completions.create(
                model=self.llm_config.get("model_name"),
                messages=messages,
                tools=tools,
                tool_choice="auto",
                **kwargs
            )
            response_message = response.choices[0].message
            return response_message