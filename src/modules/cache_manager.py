import time
from typing import Dict, Optional, Any
import logging
import json
import os
import hashlib

logger = logging.getLogger(__name__)


class CacheManager:
    def __init__(self, ttl: int = 3600, storage_path: str = "./cache_storage.json"):
        self.ttl = ttl
        self.cache_store: Dict[str, Dict] = {}  # {user_id: {employee_id: {agent: {...}}}}
        self.storage_path = storage_path
        self._load_cache()
        self._cleanup_expired_cache()

    def _generate_cache_key(self, user_id: str, employee_id: str, agent_name: str) -> str:
        """Generate a unique cache key."""
        return f"{user_id}:{employee_id}:{agent_name}"

    def _load_cache(self):
        """Load cache from disk."""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    self.cache_store = json.load(f)
                logger.info(f"Loaded cache from {self.storage_path}")
            else:
                self.cache_store = {}
                logger.info("No existing cache file, initialized empty cache")
        except Exception as e:
            logger.error(f"Error loading cache: {str(e)}")
            self.cache_store = {}

    def _save_cache(self):
        """Save cache to disk."""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)

            with open(self.storage_path, 'w') as f:
                json.dump(self.cache_store, f)
            logger.debug(f"Cache saved to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving cache: {str(e)}")

    def _is_expired(self, timestamp: float) -> bool:
        """Check if a cache entry is expired."""
        return (time.time() - timestamp) > self.ttl

    def _cleanup_expired_cache(self):
        """Cleanup expired cache entries on initialization."""
        expired_count = 0

        for user_id in list(self.cache_store.keys()):
            for employee_id in list(self.cache_store[user_id].keys()):
                for agent_name in list(self.cache_store[user_id][employee_id].keys()):
                    cache_entry = self.cache_store[user_id][employee_id][agent_name]
                    if self._is_expired(cache_entry["timestamp"]):
                        del self.cache_store[user_id][employee_id][agent_name]
                        expired_count += 1

                # Clean up empty employee entries
                if not self.cache_store[user_id][employee_id]:
                    del self.cache_store[user_id][employee_id]

            # Clean up empty user entries
            if not self.cache_store[user_id]:
                del self.cache_store[user_id]

        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired cache entries")
            self._save_cache()

    def get_cache(self, user_id: str, employee_id: str, agent_name: str) -> Optional[Dict]:
        """Get cache entry if it exists and is not expired."""
        # Convert employee_id to string for consistency
        employee_id = str(employee_id)

        if user_id not in self.cache_store:
            return None

        employee_cache = self.cache_store[user_id].get(employee_id, {})
        agent_cache = employee_cache.get(agent_name)

        if agent_cache and not self._is_expired(agent_cache["timestamp"]):
            logger.info(f"Cache hit for user {user_id}, employee {employee_id}, agent {agent_name}")
            return agent_cache["data"]
        elif agent_cache:
            # Remove expired cache
            del employee_cache[agent_name]
            if not employee_cache:
                del self.cache_store[user_id][employee_id]
            self._save_cache()
            logger.info(f"Cache expired for user {user_id}, employee {employee_id}, agent {agent_name}")

        return None

    def set_cache(self, user_id: str, employee_id: str, agent_name: str, data: Dict):
        """Set cache entry."""
        # Convert employee_id to string for consistency
        employee_id = str(employee_id)

        # Create nested structure if it doesn't exist
        if user_id not in self.cache_store:
            self.cache_store[user_id] = {}

        if employee_id not in self.cache_store[user_id]:
            self.cache_store[user_id][employee_id] = {}

        # Store data with timestamp
        self.cache_store[user_id][employee_id][agent_name] = {
            "data": data,
            "timestamp": time.time(),
            "size": len(json.dumps(data))  # Track size for debugging
        }

        # Save to disk
        self._save_cache()
        logger.info(f"Cache updated for user {user_id}, employee {employee_id}, agent {agent_name}")

    def clear_cache(self, user_id: str = None, employee_id: str = None):
        """Clear cache entries."""
        if user_id and employee_id:
            employee_id = str(employee_id)  # Convert to string for consistency
            if user_id in self.cache_store and employee_id in self.cache_store[user_id]:
                del self.cache_store[user_id][employee_id]
                logger.info(f"Cleared cache for user {user_id}, employee {employee_id}")
        elif user_id:
            if user_id in self.cache_store:
                del self.cache_store[user_id]
                logger.info(f"Cleared all cache for user {user_id}")
        else:
            self.cache_store.clear()
            logger.info("Cleared all cache")

        self._save_cache()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the cache."""
        total_entries = 0
        total_size = 0
        user_count = len(self.cache_store)

        for user_id, user_data in self.cache_store.items():
            for employee_id, employee_data in user_data.items():
                for agent_name, agent_data in employee_data.items():
                    total_entries += 1
                    total_size += agent_data.get("size", 0)

        return {
            "total_entries": total_entries,
            "total_size_bytes": total_size,
            "user_count": user_count,
            "ttl_seconds": self.ttl
        }