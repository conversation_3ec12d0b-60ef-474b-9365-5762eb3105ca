import json
import logging
import os

from typing import Dict

from src.utils.utils import format_json_response
from langfuse.decorators import langfuse_context, observe

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SubdomainRouter:
    def __init__(self, llm, domains_dir: str = "src/domains"):
        self.domains_dir = domains_dir
        self.llm = llm
        self.domain_metadata = self._load_domain_metadata()

    def _load_domain_metadata(self) -> Dict[str, Dict]:
        metadata = {}
        for domain in os.listdir(self.domains_dir):
            domain_dir = os.path.join(self.domains_dir, domain)
            if not os.path.isdir(domain_dir):
                continue

            metadata[domain] = {}
            for subdomain in os.listdir(domain_dir):
                subdomain_dir = os.path.join(domain_dir, subdomain)
                if not os.path.isdir(subdomain_dir):
                    continue

                metadata_file = os.path.join(subdomain_dir, "metadata.json")
                if not os.path.exists(metadata_file):
                    logger.warning(f"Metadata file not found for {domain}/{subdomain}, skipping.")
                    continue

                try:
                    with open(metadata_file, "r", encoding="utf-8") as f:
                        subdomain_metadata = json.load(f)
                except Exception as e:
                    logger.error(f"Failed to load metadata for {domain}/{subdomain}: {str(e)}")
                    continue

                metadata[domain][subdomain] = {
                    "metadata": subdomain_metadata
                }
        return metadata

    @observe(name="identify_subdomain_tool")
    async def route_to_subdomains(self, state: Dict) -> Dict:
        """Route the query to a subdomain and update state."""
        domain = state["domain"]
        message = state["message"]

        if domain not in self.domain_metadata:
            logger.error(f"Domain {domain} not found.")
            state["subdomain"] = "general"
            return state

        subdomain_info = {
            subdomain: {
                "intent_keywords": data["metadata"].get("intent_keywords", [])
            }
            for subdomain, data in self.domain_metadata[domain].items()
        }

        prompt = (
            f"You are a routing assistant for an internal company chatbot in the '{domain}' domain.\n"
            "You have access to the chat history, including previous interactions with the user.\n"
            "Based on the user query and chat history, select the most appropriate subdomain from the available options.\n\n"
            f"Available Subdomains for {domain}:\n" + json.dumps(subdomain_info, indent=2, ensure_ascii=False) +
            f"\n\nUser Query: {message}\n\n"
            "**Instructions**:\n"
            "1. Analyze the query carefully to determine the relevant subdomains.\n"
            "   - If the query is related to a single subdomain, return a list with only that subdomain.\n"
            "   - If the query involves multiple subdomains, return all relevant subdomains in a list.\n"
            "   - If no subdomain is clearly relevant, return an empty list: [].\n"
            "2. Output format: A JSON list of subdomains, e.g., [\"subdomain1\", \"subdomain2\"]."
        )

        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            # cleaned_response = response.strip().replace("```json", "").replace("```", "").strip()
            # if not cleaned_response.startswith("["):
            #     cleaned_response = "[" + cleaned_response.split("[", 1)[1]
            # if not cleaned_response.endswith("]"):
            #     cleaned_response = cleaned_response.split("]", 1)[0] + "]"
            # result = json.loads(cleaned_response)
            result = json.loads(format_json_response(response))
            state["subdomains"] = result
            if not state["subdomains"]:
                state["subdomains"] = "general"
            logger.info(f"Routed to subdomains: {state['subdomains']} for user {state['user_id']}")
        except Exception as e:
            logger.error(f"Error routing to subdomain: {str(e)}")
            state["error_message"] = "Issue understanding your request."

        return state

