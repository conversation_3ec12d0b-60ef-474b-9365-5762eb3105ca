import time
import json
import os
import logging
from typing import Dict, Optional, Any, List, Set
import hashlib
import re
from datetime import datetime

logger = logging.getLogger(__name__)


class CacheManager:
    def __init__(self, ttl: int = 3600, storage_path: str = "./enhanced_cache_storage.json"):
        self.ttl = ttl
        self.storage_path = storage_path
        self.cache_store = {}
        self._load_cache()
        self._cleanup_expired_cache()

        # Define patterns for time-related query extraction
        self.time_patterns = {
            'month_year': re.compile(r'tháng\s+(\d+)(?:\s+năm\s+(\d{4}))?', re.IGNORECASE),
            'this_month': re.compile(r'tháng\s+này|tháng\s+hiện\s+tại', re.IGNORECASE),
            'specific_date': re.compile(r'ngày\s+(\d+)(?:\s+tháng\s+(\d+))?', re.IGNORECASE),
        }

    def _load_cache(self) -> None:
        """Load cache from disk."""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    self.cache_store = json.load(f)
                logger.info(f"Loaded enhanced cache from {self.storage_path}")
            else:
                self.cache_store = {
                    "employee_data": {},  # Complete employee data by time period
                    "query_cache": {}  # Query-specific results
                }
                logger.info("No existing enhanced cache file, initialized empty cache")
        except Exception as e:
            logger.error(f"Error loading enhanced cache: {str(e)}")
            self.cache_store = {"employee_data": {}, "query_cache": {}}

    def _save_cache(self) -> None:
        """Save cache to disk."""
        try:
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            with open(self.storage_path, 'w') as f:
                json.dump(self.cache_store, f)
            logger.debug(f"Enhanced cache saved to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving enhanced cache: {str(e)}")

    def _is_expired(self, timestamp: float) -> bool:
        """Check if a cache entry is expired."""
        return (time.time() - timestamp) > self.ttl

    def _cleanup_expired_cache(self) -> None:
        """Cleanup expired cache entries."""
        expired_count = 0

        # Clean employee data cache
        for employee_id in list(self.cache_store["employee_data"].keys()):
            for time_period in list(self.cache_store["employee_data"][employee_id].keys()):
                entry = self.cache_store["employee_data"][employee_id][time_period]
                if self._is_expired(entry["timestamp"]):
                    del self.cache_store["employee_data"][employee_id][time_period]
                    expired_count += 1

            if not self.cache_store["employee_data"][employee_id]:
                del self.cache_store["employee_data"][employee_id]

        # Clean query cache
        for cache_key in list(self.cache_store["query_cache"].keys()):
            entry = self.cache_store["query_cache"][cache_key]
            if self._is_expired(entry["timestamp"]):
                del self.cache_store["query_cache"][cache_key]
                expired_count += 1

        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired cache entries")
            self._save_cache()

    def _extract_time_info(self, message: str) -> Dict[str, Any]:
        """Extract time information from query message."""
        time_info = {
            "specific_month": None,
            "specific_year": None,
            "is_current_month": False,
            "specific_day": None,
            "time_range": None
        }

        # Check for specific month/year
        month_match = self.time_patterns['month_year'].search(message)
        if month_match:
            time_info["specific_month"] = int(month_match.group(1))
            if month_match.group(2):  # Year specified
                time_info["specific_year"] = int(month_match.group(2))
            else:
                time_info["specific_year"] = datetime.now().year

        # Check for "this month"
        if self.time_patterns['this_month'].search(message):
            time_info["is_current_month"] = True
            time_info["specific_month"] = datetime.now().month
            time_info["specific_year"] = datetime.now().year

        # Check for specific date
        date_match = self.time_patterns['specific_date'].search(message)
        if date_match:
            time_info["specific_day"] = int(date_match.group(1))
            if date_match.group(2):  # Month specified with the day
                time_info["specific_month"] = int(date_match.group(2))
            elif not time_info["specific_month"]:  # Use current month if not specified
                time_info["specific_month"] = datetime.now().month

        # Generate a time_range string for caching
        if time_info["specific_month"] and time_info["specific_year"]:
            if time_info["specific_day"]:
                time_info[
                    "time_range"] = f"{time_info['specific_year']}-{time_info['specific_month']:02d}-{time_info['specific_day']:02d}"
            else:
                time_info["time_range"] = f"{time_info['specific_year']}-{time_info['specific_month']:02d}"

        return time_info

    def _extract_employee_info(self, message: str, employee_id: str, employee_name: str = None) -> Dict[str, Any]:
        """Extract employee information for caching purposes."""
        return {
            "employee_id": employee_id,
            "employee_name": employee_name,
            "name_in_query": employee_name.lower() in message.lower() if employee_name else False
        }

    def _generate_query_cache_key(self, message: str, user_id: str, employee_id: str, agent_name: str) -> str:
        """Generate a comprehensive cache key that captures query intent."""
        normalized_message = message.lower().strip()
        # Create a hash of the entire query to ensure uniqueness
        message_hash = hashlib.md5(normalized_message.encode()).hexdigest()[:10]
        return f"{user_id}:{employee_id}:{agent_name}:{message_hash}"

    def _generate_data_cache_key(self, employee_id: str, time_range: str, agent_name: str) -> str:
        """Generate a cache key for raw employee data by time period."""
        return f"{employee_id}:{time_range}:{agent_name}"

    def get_cache(self, user_id: str, employee_id: str, agent_name: str, message: str) -> Optional[Dict]:
        """Get cache entry with intelligent matching based on query intent."""
        # Convert employee_id to string for consistency
        employee_id = str(employee_id)

        # Try exact query cache match first
        query_cache_key = self._generate_query_cache_key(message, user_id, employee_id, agent_name)
        if query_cache_key in self.cache_store["query_cache"]:
            cache_entry = self.cache_store["query_cache"][query_cache_key]
            if not self._is_expired(cache_entry["timestamp"]):
                logger.info(f"Exact query cache hit for key: {query_cache_key}")
                return cache_entry["data"]
            else:
                del self.cache_store["query_cache"][query_cache_key]

        # Extract time information from the query
        time_info = self._extract_time_info(message)

        # If we have a specific time range, check for employee data cache
        if time_info["time_range"] and employee_id in self.cache_store["employee_data"]:
            data_cache_key = self._generate_data_cache_key(employee_id, time_info["time_range"], agent_name)
            employee_cache = self.cache_store["employee_data"].get(employee_id, {})

            # Try exact time range match
            if time_info["time_range"] in employee_cache:
                cache_entry = employee_cache[time_info["time_range"]]
                if not self._is_expired(cache_entry["timestamp"]):
                    logger.info(f"Employee data cache hit for key: {data_cache_key}")
                    return cache_entry["data"]

            # For month queries, try to see if we have data for specific days in that month
            if len(time_info["time_range"]) == 7:  # Year-Month format
                prefix = time_info["time_range"]
                day_data = {}
                for cached_time_range in employee_cache.keys():
                    if cached_time_range.startswith(prefix) and len(cached_time_range) > 7:
                        day_entry = employee_cache[cached_time_range]
                        if not self._is_expired(day_entry["timestamp"]):
                            # Collect all day-specific data for this month
                            day_data[cached_time_range] = day_entry["data"]

                if day_data:
                    # We have some days from this month - this could be used
                    # to partially fulfill the query or for intelligent merging
                    logger.info(f"Found {len(day_data)} day-level cache entries for month {prefix}")
                    # Return the most recent data as a fallback
                    # In a real implementation, you would merge these intelligently
                    latest_key = sorted(day_data.keys())[-1]
                    return employee_cache[latest_key]["data"]

        return None

    def set_cache(self, user_id: str, employee_id: str, agent_name: str, message: str, data: Dict) -> None:
        """Set cache entries at multiple levels."""
        # Convert employee_id to string for consistency
        employee_id = str(employee_id)

        # Create query-specific cache entry
        query_cache_key = self._generate_query_cache_key(message, user_id, employee_id, agent_name)
        self.cache_store["query_cache"][query_cache_key] = {
            "data": data,
            "timestamp": time.time(),
            "size": len(json.dumps(data))
        }

        # Extract time information and create time-based cache entries
        time_info = self._extract_time_info(message)
        if time_info["time_range"]:
            # Ensure employee_data structure exists
            if employee_id not in self.cache_store["employee_data"]:
                self.cache_store["employee_data"][employee_id] = {}

            # Store data by time period
            self.cache_store["employee_data"][employee_id][time_info["time_range"]] = {
                "data": data,
                "timestamp": time.time(),
                "size": len(json.dumps(data))
            }

        # Save to disk
        self._save_cache()
        logger.info(f"Cache updated for query: {message}")

    def clear_cache(self, user_id: str = None, employee_id: str = None) -> None:
        """Clear cache entries based on specified parameters."""
        if employee_id:
            employee_id = str(employee_id)
            # Clear employee data
            if employee_id in self.cache_store["employee_data"]:
                del self.cache_store["employee_data"][employee_id]

            # Clear query cache for this employee
            for cache_key in list(self.cache_store["query_cache"].keys()):
                if f":{employee_id}:" in cache_key:
                    del self.cache_store["query_cache"][cache_key]

            logger.info(f"Cleared cache for employee {employee_id}")
        elif user_id:
            # Clear only user-specific query cache
            for cache_key in list(self.cache_store["query_cache"].keys()):
                if cache_key.startswith(f"{user_id}:"):
                    del self.cache_store["query_cache"][cache_key]

            logger.info(f"Cleared query cache for user {user_id}")
        else:
            # Clear all cache
            self.cache_store = {"employee_data": {}, "query_cache": {}}
            logger.info("Cleared all cache")

        self._save_cache()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the cache."""
        query_entries = len(self.cache_store["query_cache"])
        query_size = sum(entry.get("size", 0) for entry in self.cache_store["query_cache"].values())

        employee_entries = 0
        employee_size = 0
        employee_count = len(self.cache_store["employee_data"])

        for employee_id, time_ranges in self.cache_store["employee_data"].items():
            employee_entries += len(time_ranges)
            employee_size += sum(entry.get("size", 0) for entry in time_ranges.values())

        return {
            "query_cache_entries": query_entries,
            "query_cache_size_bytes": query_size,
            "employee_data_entries": employee_entries,
            "employee_data_size_bytes": employee_size,
            "unique_employees": employee_count,
            "ttl_seconds": self.ttl
        }