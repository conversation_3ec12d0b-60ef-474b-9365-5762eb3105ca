from typing import Dict, Optional
from collections import deque
import logging
import json
import os
import time

logger = logging.getLogger(__name__)


class ContextManager:
    def __init__(self, max_length: int = 10, storage_path: str = "./context_storage.json"):
        self.max_length = max_length
        self.short_term_store: Dict[str, deque] = {}
        self.storage_path = storage_path
        self.long_term_store = self._load_long_term_memory()

    def _load_long_term_memory(self) -> Dict:
        if os.path.exists(self.storage_path):
            with open(self.storage_path, 'r') as f:
                return json.load(f)
        return {}

    def _save_long_term_memory(self):
        with open(self.storage_path, 'w') as f:
            json.dump(self.long_term_store, f)

    def get_context(self, user_id: str) -> Dict:
        short_term = self.short_term_store.get(user_id, deque(maxlen=self.max_length))
        long_term = self.long_term_store.get(user_id, [])
        return {
            "short_term_history": list(short_term),
            "long_term_history": long_term
        }

    def update_context(self, user_id: str, message: str, response: str, api_result: Dict = None) -> None:
        if user_id not in self.short_term_store:
            self.short_term_store[user_id] = deque(maxlen=self.max_length)

        entry = {
            "message": message,
            "response": response,
            "timestamp": time.time()
        }
        if api_result:
            entry["api_result"] = api_result

        self.short_term_store[user_id].append(entry)

        if len(self.short_term_store[user_id]) >= self.max_length:
            entry = self.short_term_store[user_id].popleft()
            if user_id not in self.long_term_store:
                self.long_term_store[user_id] = []
            self.long_term_store[user_id].append(entry)
            self._save_long_term_memory()

    def check_existing_question(self, user_id: str, message: str) -> Optional[Dict]:
        context = self.get_context(user_id)

        # Check short-term first (more recent)
        for entry in reversed(context["short_term_history"]):
            if entry["message"] == message and "response" in entry:
                return entry

        # Then check long-term
        for entry in reversed(context["long_term_history"]):
            if entry["message"] == message and "response" in entry:
                return entry
        return None

    def clear_context(self, user_id: str = None) -> None:
        if user_id:
            if user_id in self.short_term_store:
                del self.short_term_store[user_id]
            if user_id in self.long_term_store:
                del self.long_term_store[user_id]
                self._save_long_term_memory()
            logger.info(f"Cleared context for user {user_id}")
        else:
            self.short_term_store.clear()
            self.long_term_store.clear()
            self._save_long_term_memory()
            logger.info("Cleared all context")