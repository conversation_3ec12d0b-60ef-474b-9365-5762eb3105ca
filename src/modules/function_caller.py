import httpx
import os
from typing import Dict
import logging
from dotenv import load_dotenv
from langfuse.decorators import langfuse_context, observe
from src.utils.parse_utils import parse_list_string

load_dotenv()

logger = logging.getLogger(__name__)

class FunctionCaller:
    def __init__(
            self,
            base_url: str = "https://api.weekly.vn",
            db_uri: str = "postgresql://user:pass@localhost:5432/db",
            ttl: int = 86400
    ):
        self.base_url = base_url
        self.api_token = os.getenv("API_TOKEN")
        if not self.api_token:
            raise ValueError("API_TOKEN environment variable is required")
        self.db_uri = db_uri
        self.ttl = ttl

    @observe(name="call_api_tool")
    async def execute(self, function_data: Dict) -> Dict:
        path = function_data.get("path")
        method = function_data.get("method", "post")
        params = function_data.get("params", {})
        # Specific for weekly api
        if "month" in params:
            params['month'] = params['month'] - 1

        if "query" in params and "name" in params["query"] and isinstance(params["query"]["name"], str):
            list_name = parse_list_string(params["query"]["name"])
            if list_name and len(list_name) > 0:
                # If multiple names, make separate API calls and combine results
                if len(list_name) > 1:
                    all_results = {"data": []}
                    for name in list_name:
                        name_params = params.copy()
                        name_params["query"]["name"] = name
                        result = await self._make_api_request(path, method, name_params)
                        if "data" in result and isinstance(result["data"], list):
                            all_results["data"].extend(result["data"])
                        elif "error" in result:
                            return result  # Return on first error
                    return all_results
                else:
                    # Single name in list format, use it directly
                    params["query"]["name"] = list_name[0]

        if not path or not method:
            raise ValueError("Function call must have 'path' and 'method'.")

        return await self._make_api_request(path, method, params)

    async def _make_api_request(self, path: str, method: str, params: Dict) -> Dict:
        """Helper method to make the actual API request"""
        url = f"{self.base_url}{path}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_token}"
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    json=params,
                    headers=headers,
                    timeout=100
                )
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"API request failed: {str(e)}")
            return {"error": f"HTTP error {e.response.status_code}: {e.response.text}"}
        except Exception as e:
            logger.error(f"Function execution error: {str(e)}")
            return {"error": str(e)}

    async def call_api(self, path: str, method: str, payload: Dict) -> Dict:
        function_data = {
            "path": path,
            "method": method,
            "params": payload
        }
        return await self.execute(function_data)
