# src/config.py
from pydantic import BaseModel
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

class ChatbotConfig(BaseModel):
    llm_type: str = os.getenv("LLM_TYPE", "chatgpt")
    api_base_url: str = os.getenv("API_BASE_URL", "https://api.weekly.vn")
    max_context_length: int = int(os.getenv("MAX_CONTEXT_LENGTH", 10))
    timeout: int = int(os.getenv("TIMEOUT", 60))
    db_uri: str = os.getenv("DB_URI", "postgresql://user:pass@localhost:5432/db")
    openai_api_key: Optional[str] = os.getenv("OPENAI_API_KEY")