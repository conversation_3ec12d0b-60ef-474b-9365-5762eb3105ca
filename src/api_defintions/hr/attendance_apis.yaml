openapi: 3.0.0
info:
  title: Weekly AI
  version: 1.0.0
  description: 'API specification converted from Postman collection: Weekly AI'
servers:
- url: https://api.weekly.vn
  description: Production server
paths:
  /employee/list:
    post:
      summary: <PERSON>h sách nhân sự
      description: This API retrieves a list of employees in the company based on specified filters. It is used for employee directory lookups or to find employees by name, HR code, or email. The API supports pagination and sorting, and can filter employees by status (e.g., 'working'). Use this API when you need to search for an employee (e.g., to resolve a name to an employee ID) or list all employees. The response includes employee details such as ID, name, and email.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    status:
                      type: string
                      default: working
                  required:
                  - status
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 50
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code
                  - name
                  - email
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code_sort
                status:
                  type: string
                  default: working
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - status
            example:
              query:
                status: working
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 50
              searchFields:
              - hr_code
              - name
              - email
              sort:
              - hr_code_sort
              status: working
  /shift/list:
    post:
      summary: Danh sách ca làm việc
      description: This API retrieves a list of work shifts available in the company. It is used for scheduling purposes, such as viewing shift details or planning employee schedules. The API supports pagination, searching by shift name, and sorting. Use this API when you need to list all shifts or find specific shifts (e.g., by name). The response includes shift details such as name and schedule.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties: {}
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 20
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - name
                sort:
                  type: array
                  items:
                    type: string
                  default: []
                populates:
                  type: array
                  items:
                    type: string
                  default: []
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
            example:
              query: {}
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 20
              searchFields:
              - name
              sort: []
              populates: []
  /attendance-assign/list:
    post:
      summary: Danh sách phân ca
      description: This API retrieves a list of attendance assignments, such as shifts assigned to departments or employees. It is used for managing shift assignments and ensuring employees are scheduled correctly. The API supports pagination, searching by name or code, and sorting (e.g., by last updated time). Use this API when you need to view shift assignments for a department or specific employees. The response includes assignment details such as department, employee, and shift.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    type:
                      type: string
                      default: department
                  required:
                  - type
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 20
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - name
                  - code
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - -updatedAt
                populates:
                  type: array
                  items:
                    type: string
                  default: []
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
            example:
              query:
                type: department
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 20
              searchFields:
              - name
              - code
              sort:
              - -updatedAt
              populates: []
  /checkin-log/find:
    post:
      summary: Dữ liệu chấm công (chi tiết)
      description: This API retrieves detailed check-in and check-out logs for a specific employee within a given time range. It is used for attendance tracking, such as monitoring an employee's check-in/check-out times or calculating attendance metrics. The API requires an employee ID, company ID, and a date range (e.g., March 2025). Use this API when you need to view detailed attendance records for an employee over a specific period. The response includes logs with timestamps and types (e.g., 'checkin', 'checkout').
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    employee_id:
                      type: integer
                      default: 3
                    company_id:
                      type: integer
                      default: 1
                    type:
                      type: object
                      properties:
                        $in:
                          type: array
                          items:
                            type: string
                          default:
                          - checkin
                          - checkout
                          - import
                      required:
                      - $in
                    datetime:
                      type: object
                      properties:
                        $gte:
                          type: string
                          format: date-time
                          default: '2025-03-03T17:00:00.000Z'
                        $lte:
                          type: string
                          format: date-time
                          default: '2025-03-04T16:59:59.999Z'
                      required:
                      - $gte
                      - $lte
                  required:
                  - employee_id
                  - company_id
                  - type
                  - datetime
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - datetime
                addCompanyUtcOffsetToQueryDatetime:
                  type: integer
                  default: true
              required:
              - query
              - sort
              - addCompanyUtcOffsetToQueryDatetime
            example:
              query:
                employee_id: 3
                company_id: 1
                type:
                  $in:
                  - checkin
                  - checkout
                  - import
                datetime:
                  $gte: '2025-03-03T17:00:00.000Z'
                  $lte: '2025-03-04T16:59:59.999Z'
              sort:
              - datetime
              addCompanyUtcOffsetToQueryDatetime: true
  /employee/list-with-timesheet:
    post:
      summary: Bảng công
      description: This API retrieves a list of employees along with their timesheet data for a specific month and year. It is used for generating attendance reports or calculating work hours for payroll purposes. The API supports pagination, searching by HR code, name, or email, and sorting. Use this API when you need to view timesheet data for employees over a specific period (e.g., March 2025). The response includes employee details and their timesheet entries for the specified month.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    status:
                      type: string
                      default: working
                  required:
                  - status
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 10
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code
                  - name
                  - uid
                  - email
                  - phone
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code_sort
                populates:
                  type: array
                  items:
                    type: string
                  default: []
                month:
                  type: integer
                  default: 2
                year:
                  type: integer
                  default: 2025
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
              - month
              - year
            example:
              query:
                status: working
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 10
              searchFields:
              - hr_code
              - name
              - uid
              - email
              - phone
              sort:
              - hr_code_sort
              populates: []
              month: 2
              year: 2025
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
- bearerAuth: []
