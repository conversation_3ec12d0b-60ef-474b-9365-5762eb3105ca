from datetime import timedelta, datetime
import re

from dateutil.relativedelta import relativedelta


def is_leap_year(year: int) -> bool:
    return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)


def get_month_end(year: int, month: int) -> int:
    if month in [4, 6, 9, 11]:
        return 30
    elif month == 2:
        return 29 if is_leap_year(year) else 28
    else:
        return 31


def parse_clear_period(period: str, current: datetime) -> dict:
    period = period.strip().lower()

    patterns = [
        r'^hôm nay$',
        r'^hôm qua$',
        r'^ngày mai$',
        r'^tháng này$',
        r'^tháng trước$',
        r'^tháng sau$',
        r'^tháng\s*(\d{1,2})$',
        r'^tháng\s*(\d{1,2})\s*năm\s*(\d{4})$',
        r'^năm nay$',
        r'^năm trước$',
        r'^năm sau$',
        r'^năm\s*(\d{4})$'
    ]

    for pattern in patterns:
        match = re.match(pattern, period)
        if match:
            if pattern == r'^hôm nay$':
                return {
                    "start_date": current.strftime("%Y-%m-%d"),
                    "end_date": current.strftime("%Y-%m-%d")
                }
            elif pattern == r'^hôm qua$':
                date = current - timedelta(days=1)
                return {
                    "start_date": date.strftime("%Y-%m-%d"),
                    "end_date": date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^ngày mai$':
                date = current + timedelta(days=1)
                return {
                    "start_date": date.strftime("%Y-%m-%d"),
                    "end_date": date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^tháng này$':
                start_date = current.replace(day=1)
                end_date = start_date.replace(day=get_month_end(current.year, current.month))
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^tháng trước$':
                start_date = (current.replace(day=1) - relativedelta(months=1))
                end_date = start_date.replace(day=get_month_end(start_date.year, start_date.month))
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^tháng sau$':
                start_date = (current.replace(day=1) + relativedelta(months=1))
                end_date = start_date.replace(day=get_month_end(start_date.year, start_date.month))
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^tháng\s*(\d{1,2})$':
                month = int(match.group(1))
                if 1 <= month <= 12:
                    year = current.year
                    if month > current.month:
                        year -= 1
                    start_date = current.replace(year=year, month=month, day=1)
                    end_date = start_date.replace(day=get_month_end(year, month))
                    return {
                        "start_date": start_date.strftime("%Y-%m-%d"),
                        "end_date": end_date.strftime("%Y-%m-%d")
                    }

            elif pattern == r'^tháng\s*(\d{1,2})\s*năm\s*(\d{4})$':
                month = int(match.group(1))
                year = int(match.group(2))
                if 1 <= month <= 12:
                    start_date = current.replace(year=year, month=month, day=1)
                    end_date = start_date.replace(day=get_month_end(year, month))
                    return {
                        "start_date": start_date.strftime("%Y-%m-%d"),
                        "end_date": end_date.strftime("%Y-%m-%d")
                    }
            elif pattern == r'^năm nay$':
                start_date = current.replace(month=1, day=1)
                end_date = current.replace(month=12, day=31)
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^năm trước$':
                year = current.year - 1
                start_date = current.replace(year=year, month=1, day=1)
                end_date = current.replace(year=year, month=12, day=31)
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^năm sau$':
                year = current.year + 1
                start_date = current.replace(year=year, month=1, day=1)
                end_date = current.replace(year=year, month=12, day=31)
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }
            elif pattern == r'^năm\s*(\d{4})$':
                year = int(match.group(1))
                start_date = current.replace(year=year, month=1, day=1)
                end_date = current.replace(year=year, month=12, day=31)
                return {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                }

    return None


