from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

class Plan(BaseModel):
    steps: List[Dict[str, Any]] = Field(default_factory=list)
    contingencies: Dict[str, List[Dict[str, Any]]] = Field(default_factory=dict)
    current_step: int = 0

    def get_current_step(self) -> Optional[Dict[str, str]]:
        return self.steps[self.current_step] if 0 <= self.current_step < len(self.steps) else None

    def get_contingency(self, step_id: str) -> Optional[List[Dict[str, Any]]]:
        return self.contingencies.get(step_id, [])

    def advance(self) -> None:
        self.current_step += 1

    def is_completed(self) -> bool:
        return self.current_step >= len(self.steps)

class ChatbotState(dict):
    user_id: str
    domain: str
    message: str
    context: Dict[str, Any]
    plan: Optional[Plan]
    subdomains: List[str]
    entities: Dict[str, Any]
    agent_results: Dict[str, Dict[str, Any]]
    response: Optional[str]
    error_message: Optional[str]
    retry_count: Dict[str, int]
    employee_id: Optional[str]
    attempts: int
    dependencies: Dict[str, Any]
    intent: str
    # conversation_history: List[Dict[str, str]]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setdefault("user_id", "")
        self.setdefault("domain", "")
        self.setdefault("message", "")
        self.setdefault("context", {})
        self.setdefault("plan", None)
        self.setdefault("subdomains", [])
        self.setdefault("entities", {})
        self.setdefault("agent_results", {})
        self.setdefault("response", None)
        self.setdefault("error_message", None)
        self.setdefault("retry_count", {})
        self.setdefault("employee_id", None)
        self.setdefault("attempts", 0)
        self.setdefault("dependencies", {})
        self.setdefault("intent", "")
        # self.setdefault("conversation_history", [])