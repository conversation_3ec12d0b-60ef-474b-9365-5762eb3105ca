import json
import logging
import sqlite3
from typing import List, Dict, Any, Tuple

from src.utils.utils import format_json_response, process_profile_result

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DB_FILE = "src/database/hr_management.db"


class DatabaseHandler:
    def __init__(self, llm):
        self.llm = llm

    @staticmethod
    def to_lower_unicode(s: str) -> str:
        """Chuyển chuỗi Unicode về chữ thường, xử lý cả tiếng Việt."""
        return s.lower() if s else s

    def connect_db(self):
        conn = sqlite3.connect(DB_FILE)
        # Đ<PERSON>ng ký hàm Python vào SQLite với tên "lower_unicode"
        conn.create_function("lower_unicode", 1, self.to_lower_unicode)
        return conn

    def execute_query(self, query: str, params: List = None) -> List[Dict]:
        conn = self.connect_db()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"Database query error: {e}")
            raise
        finally:
            conn.close()

    async def generate_sql_query(self, state: Dict, metric: str, scope: Dict[str, Any]) -> Tuple[str, List]:
        """
        Generates an SQL query using the LLM based on the metric, scope, and original question.
        Returns a tuple of (query, params).
        """
        schema = """
Table: employees
Columns:
- employee_id: INTEGER PRIMARY KEY
- name: TEXT NOT NULL
- email: TEXT NOT NULL UNIQUE
- hr_code: TEXT NOT NULL
- start_date: TEXT NOT NULL
- position: TEXT NOT NULL
- department: TEXT NOT NULL
- status: TEXT NOT NULL DEFAULT 'working'
- employment_type: TEXT NOT NULL
- years_worked: INTEGER NOT NULL DEFAULT 0
"""

        departments = self.execute_query("SELECT DISTINCT department FROM employees")
        valid_departments = [d["department"] for d in departments]

        prompt = f"""
Generate an SQL query for the SQLite database based on the following information.

Database Schema:
{schema}

Valid Departments: {json.dumps(valid_departments, ensure_ascii=False)}

Question: '{state["message"]}'
Metric: {metric}
Scope: {json.dumps(scope, ensure_ascii=False)}

Instructions:
1. Create a valid SQL query to retrieve data **only** from the employees table based on the metric and scope.
2. Restrict queries to employee data:
   - Only the employees table exists in the database. Do NOT query or assume any other tables (e.g., late_arrivals, payroll, leave).
   - Supported metrics (must use employees table):
     - "employee_ids": Return employee_id, hr_code, and name.
     - "count": Count employees.
     - "list_details": Return employee_id, name, email, position, department, employment_type, years_worked.
     - "full_time_count": Count employees with employment_type = 'Fulltime chính thức'.
     - "department_list": List distinct departments.
   - If the metric is unsupported (e.g., late_arrivals_count, payroll_data), default to "employee_ids" (returning employee_id, hr_code, and name) and note in error_message: "Metric '[metric]' not supported; defaulting to employee_ids."
   - Ignore question elements unrelated to the employees table (e.g., 'đi muộn', 'late arrivals'); these are handled by agents, not SQL.
3. Scope handling:
   - "individual": Filter by name or email (use LIKE for partial matches).
   - "department" or "team": Filter by department (use LIKE for partial matches).
   - "position": Filter by position (exact match).
   - "company": No additional filter.
4. Always include WHERE status = 'working' unless metric is department_list.
5. Validate department names against valid_departments; if invalid, note in error_message.
6. Use parameterized queries with ? placeholders for safety.
7. Return JSON:
   - query: str (the SQL query, must reference only employees table)
   - params: [str] (list of parameter values)
   - error_message: str or null

Examples:
Metric: "count"
Scope: {{"type": "company", "value": ""}}
Output: {{
    "query": "SELECT COUNT(*) as count FROM employees WHERE status = 'working'",
    "params": [],
    "error_message": null
}}

Metric: "employee_ids"
Scope: {{"type": "department", "value": "Sales"}}
Output: {{
    "query": "SELECT employee_id, hr_code, name FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
    "params": ["%sales%"],
    "error_message": null
}}

Metric: "late_arrivals_count"
Scope: {{"type": "department", "value": "Phòng nhân sự"}}
Question: "Tháng này ai đi muộn nhiều nhất Phòng nhân sự?"
Output: {{
    "query": "SELECT employee_id, hr_code, name FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
    "params": ["%phòng nhân sự%"],
    "error_message": "Metric 'late_arrivals_count' not supported in employees table; defaulting to employee_ids."
}}

Metric: "list_details"
Scope: {{"type": "individual", "value": "Nguyen Van A"}}
Output: {{
    "query": "SELECT employee_id, name, email, position, department, employment_type, years_worked FROM employees WHERE status = 'working' AND (LOWER(name) LIKE ? OR LOWER(email) LIKE ?)",
    "params": ["%nguyen van a%", "%nguyen van a%"],
    "error_message": null
}}

Metric: "count"
Scope: {{"type": "department", "value": "InvalidDept"}}
Output: {{
    "query": "SELECT COUNT(*) as count FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
    "params": ["%invaliddept%"],
    "error_message": "Department 'InvalidDept' not found in valid departments."
}}
"""

        try:
            response = await self.llm.process_message(state["message"], {}, prompt)
            result = json.loads(format_json_response(response))
            query = result["query"]
            params = result["params"]
            error_message = result["error_message"]

            try:
                test_result = self.execute_query(query, params)
                logger.info(f"Generated query: {query}, params: {params}, rows: {len(test_result)}")
            except Exception as e:
                logger.error(f"Invalid query: {query}, error: {e}")
                raise ValueError(f"Generated query is invalid: {e}")

            if error_message:
                logger.warning(f"Query warning: {error_message}")

            return query, params
        except Exception as e:
            logger.error(f"Failed to generate SQL query: {e}")
            raise ValueError(f"Cannot generate SQL query: {e}")


class StatisticsProcessor:
    STATISTICS_METRICS = {
        "count": {
            "function": lambda data: data[0]["count"] if data else 0,
            "response_template": "Có {result} nhân viên trong {scope_description}."
        },
        "list_names": {
            "function": lambda data: [emp["name"] for emp in data],
            "response_template": "Danh sách nhân viên trong {scope_description}: {result}."
        },
        "list_details": {
            "function": lambda data: [{
                "name": emp["name"],
                "email": emp["email"],
                "department": emp["department"],
                "employment_type": emp["employment_type"],
                "start_date": emp["start_date"],
                "probationary_start_date": emp["probationary_start_date"],
                "official_start_date": emp["official_start_date"],
                "average_years": emp["avg_years"]
            } for emp in data],
            "response_template": "Thông tin nhân viên trong {scope_description}:\n{result}"
        },
        "average_years": {
            "function": lambda data: [item["avg_years"] for item in data if "avg_years" in item] if data else [],
            "response_template": "Trung bình số năm làm việc của nhân viên trong {scope_description} là {result}."
        },
        "full_time_count": {
            "function": lambda data: sum(1 for item in data if "employment_type" in item and "fulltime" in item["employment_type"].lower()) if data else 0,
            "response_template": "Số nhân viên full-time trong {scope_description}: {result} người."
        },
        "part_time_count": {
            "function": lambda data: sum(1 for item in data if "employment_type" in item and "part" in item[
                "employment_type"].lower()) if data else 0,
            "response_template": "Số nhân viên part-time trong {scope_description}: {result} người."
        },
        "intern_count": {
            "function": lambda data: sum(1 for item in data if "employment_type" in item and "intern" in item[
                "employment_type"].lower()) if data else 0,
            "response_template": "Số nhân viên intern trong {scope_description}: {result} người."
        },
        "department_list": {
            "function": lambda data: set(dept["department"] for emp in data for dept in emp["department"]),
            "response_template": "Các bộ phận trong công ty: {result}."
        },
        "employee_ids": {
            "function": lambda data: [emp["id"] for emp in data],
            "response_template": "Found {result} employee IDs in {scope_description}."
        }
    }

    def __init__(self, llm):
        self.llm = llm

    # @observe()
    def process_statistics(self, data, metric, scope):
        if metric not in self.STATISTICS_METRICS:
            return {"response": "Loại thống kê không được hỗ trợ.", "data": {}}

        metric_config = self.STATISTICS_METRICS[metric]
        try:
            result = metric_config["function"](data)
            scope_description = self._get_scope_description(scope)

            if not result and metric not in ["count", "full_time_count", "average_years"]:
                return {"response": f"Không tìm thấy dữ liệu trong {scope_description}.", "data": result}

            formatted_result = self._format_result(result, metric)
            response = metric_config["response_template"].format(result=formatted_result,
                                                                 scope_description=scope_description)
            return {"response": response, "data": result}
        except Exception as e:
            logger.error(f"Error processing statistic {metric}: {e}")
            return {"response": "Có lỗi khi xử lý thống kê.", "data": {}}

    def _get_scope_description(self, scope):
        scope_type = scope.get("type")
        scope_value = scope.get("value", "")
        if scope_type == "individual":
            return f"nhân viên {scope_value}"
        elif scope_type == "department":
            return f"bộ phận {scope_value}"
        elif scope_type == "position":
            return f"chức vụ {scope_value}"
        elif scope_type == "company":
            return "toàn công ty"
        return "toàn công ty"

    def _format_result(self, result, metric):
        if metric in ["count", "full_time_count"]:
            return str(result)
        elif metric == "list_names":
            return ", ".join(result) if result else "không có nhân viên nào"
        elif metric == "list_details":
            return process_profile_result(result)
        elif metric == "average_years":
            return result
        elif metric == "department_list":
            return ", ".join(result) if result else "không có bộ phận nào"
        elif metric == "employee_ids":
            return str(len(result)) if result else "0"
        return str(result)
