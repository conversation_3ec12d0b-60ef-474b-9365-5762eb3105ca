from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from langfuse.decorators import langfuse_context, observe
from dotenv import load_dotenv
from .state import ChatbotState
from .nodes import CoordinatorNodes
from .edges import define_edges

load_dotenv()

# langfuse_handler = CallbackHandler()


class ChatbotGraph:
    def __init__(self, config, domain: str, llm, domains_dir: str, function_caller, context_manager):
        self.config = config
        self.domain = domain
        self.llm = llm
        self.domains_dir = domains_dir
        self.function_caller = function_caller
        self.context_manager = context_manager
        self.graph = self._build_graph()

    def _build_graph(self) -> CompiledStateGraph:
        graph = StateGraph(ChatbotState)
        nodes = CoordinatorNodes(
            self.config, self.domain, self.llm, self.domains_dir, self.function_caller, self.context_manager
        )
        graph.add_node("process", nodes._process)
        define_edges(graph)
        return graph.compile()

    # @observe()
    async def process(self, message: str, user_id: str, chat_history) -> str:
        state = ChatbotState(
            user_id=user_id,
            domain="hr",
            message=message,
            context={"chat_history": chat_history},
            subdomains=[],
            entities={},
            agent_results={},
            response=None,
            error_message=None,
            retry_count={},
            employee_id=None,
            attempts=0,
            dependencies={},
            # conversation_history=chat_history,
        )
        result = await self.graph.ainvoke(input=state)
        return result["response"]

