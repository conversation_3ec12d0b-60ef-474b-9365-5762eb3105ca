import json
import logging
from typing import Any, List

from .state import Cha<PERSON><PERSON><PERSON>tate, Plan

from src.utils.utils import format_json_response
from langfuse.decorators import langfuse_context, observe

logger = logging.getLogger(__name__)


class QuestionAnalyzer:
    def __init__(self, llm, sub_agents):
        self.llm = llm
        self.sub_agents = sub_agents

    @observe(name="analyze_question_tool")
    async def analyze_question(self, state: dict) -> dict[str, Any]:
        agent_info = []
        for subdomain, agents in self.sub_agents.items():
            for agent_name, agent_data in agents.items():
                agent_info.append({
                    "agent_name": agent_name,
                    "subdomain": subdomain,
                    "description": agent_data.get("description", "No description provided")
                })
        history = state["context"].get("chat_history", [])
        history_str = "\n".join([f"{chat['role'].capitalize()}: {chat['content']}" for chat in history if chat['role'] == "user"])
        prompt = f"""
Analyze this conversation: 
Chat history: '{history_str}'
Current question: '{state["message"]}'

Instructions:
1. Identify the intent:
   - "statistics": Queries about employee, department or company about numbers, list details, or averages.
   - "timesheet": Queries about attendance data (e.g., late check-ins, early check-outs, absences).
   - "payroll": Queries about salary or compensation.
   - "leave": Queries about used leave days, unpaid leave, or unexcused absences.
   - "remain_leave": Queries about remaining paid leave days.
   - "performance": Queries about employee performance metrics.
2. Identify the scope with structured output:
    - "type": One of "individual", "department", or "company"
    - "value":
        + If `type` is "individual", then `value` must be a **string representing a single employee name** (e.g., `"Nguyễn Tiến Anh"`).
        + If `type` is "department", then `value` must be a **string representing a single department name**, and the value must be one of the following valid departments: ["BOD", "JP", "Phòng Marketing", "Phòng hành chính kế toán", "Phòng kinh doanh", "Phòng kỹ thuật", "Phòng nhân sự", "Phòng pháp chế", "Phòng Sản Xuất D1", "Phòng Sản Xuất D2", "Ban Quy Trình", "Ban Đào Tạo"]
        + If `type` is "company", then value should be "all".
3. Extract entities:
   - metric: For statistics (e.g., count, list_details, average_years, full_time_count, part_time_count, intern_count, department_list).
   - time_period: For timesheet/payroll/leave (e.g., "this month", "April 2025").
4. Return list JSON:
   - intent: str
   - scope: {{ "type": str, "value": str }}
   - entities: {{ key: value }}

Examples:
Question: "Công ty có bao nhiêu người?"
Output: [{{
    "intent": "statistics",
    "scope": {{ "type": "company", "value": "all" }},
    "entities": {{ "metric": "count" }}
}}]

Question: "Bộ phận Sales có ai đi muộn tháng này?"
Output: [{{
    "intent": "timesheet",
    "scope": {{ "type": "department", "value": "Phòng kinh doanh" }},
    "entities": {{ "time_period": "tháng này" }}
}}]

Question: "Thống kê nghỉ phép và đi muộn của Nguyễn Công Trí và Nguyễn Tiến Anh từ đầu năm đến giờ?"
Output: [
    {{
        "intent": "leave",
        "scope": {{ "type": "individual", "value": "Nguyễn Công Trí" }},
        "entities": {{ "time_period": "từ đầu năm đến giờ" }}
    }},
    {{
        "intent": "leave",
        "scope": {{ "type": "individual", "value": "Nguyễn Tiến Anh" }},
        "entities": {{ "time_period": "từ đầu năm đến giờ" }}
    }},
    {{
        "intent": "timesheet",
        "scope": {{ "type": "individual", "value": "Nguyễn Công Trí" }},
        "entities": {{ "time_period": "từ đầu năm đến giờ" }}
    }},
    {{
        "intent": "timesheet",
        "scope": {{ "type": "individual", "value": "Nguyễn Tiến Anh" }},
        "entities": {{ "time_period": "từ đầu năm đến giờ" }}
    }},
]
"""
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            analysis = json.loads(format_json_response(response))
            return analysis
        except Exception as e:
            logger.error(f"Question analysis failed: {e}")
            return {
                "intent": "casual",
                "scope": {"type": "company", "value": ""},
                "entities": {"error": "Failed to analyze question"}
            }

class PlanCreator:
    def __init__(self, llm, sub_agents):
        self.llm = llm
        self.sub_agents = sub_agents
        self.analyzer = QuestionAnalyzer(llm, sub_agents)

    @observe(name="create_tasks_tool")
    async def create_tasks(self, state: dict) -> List[str]:
        """
        Creates a list of natural language task descriptions in Vietnamese
        based on the user's question and analysis results.
        """
        try:
            prompt = f"""
Based on the user's question, generate a list of specific tasks to be performed in Vietnamese.

Requirements:
1. Create a list of specific, clear tasks written in Vietnamese
2. Each task must describe a specific action to be performed
3. Arrange the tasks in logical execution order
4. Use natural, easy-to-understand language
5. Return the result in JSON format with a "tasks" key containing an array of strings

Examples:
Question: "Thống kê đi muộn của Nguyễn Tiến Anh tháng 3"
Output: {{
    "tasks": [
        "Xác định thông tin nhân viên Nguyễn Tiến Anh",
        "Trích xuất thời gian: tháng 3",
        "Truy vấn dữ liệu chấm công của nhân viên trong khoảng thời gian",
        "Tạo báo cáo kết quả"
    ]
}}

Question: "Thống kê tình hình đi muộn của Ngô Gia Phong trong tháng 3 và số lần nghỉ phép của Nguyễn Tiến Anh trong tháng 5"
Output: {{
    "tasks": [
        "Xác định thông tin nhân viên Ngô Gia Phong",
        "Trích xuất thời gian cho Ngô Gia Phong: tháng 3",
        "Truy vấn dữ liệu chấm công của Ngô Gia Phong trong khoảng thời gian",
        "Tạo báo cáo kết quả",
        "Xác định thông tin nhân viên Nguyễn Tiến Anh",
        "Trích xuất thời gian cho Nguyễn Tiến Anh: tháng 5",
        "Truy vấn dữ liệu chấm công của Nguyễn Tiến Anh trong khoảng thời gian",
        "Tạo báo cáo kết quả",
    ]
}}

Question: "So sánh tình hình đi muộn trong tháng 3 của Nguyễn Tiến Anh với tình hình đi muộn trong tháng 5 của Ngô Gia Phong"
Output: {{
    "tasks": [
        "Xác định thông tin nhân viên Nguyễn Tiến Anh",
        "Trích xuất thời gian cho Ngô Gia Phong: tháng 3",
        "Truy vấn dữ liệu chấm công của Ngô Gia Phong trong khoảng thời gian",
        "Tạo báo cáo kết quả cho Nguyễn Tiến Anh",
        "Xác định thông tin nhân viên Ngô Gia Phong",
        "Trích xuất thời gian cho Ngô Gia Phong: tháng 5",
        "Truy vấn dữ liệu chấm công của Ngô Gia Phong trong khoảng thời gian",
        "Tạo báo cáo kết quả cho Ngô Gia Phong",
        "Phân tích và so sánh 2 báo cáo",
    ]
}}

Question: "Thống kê đi muộn của D2 từ đầu năm đến giờ"
Output: {{
    "tasks": [
        "Xác định thông tin các nhân viên trong bộ phận D2",
        "Trích xuất thời gian: từ đầu năm đến giờ",
        "Truy vấn dữ liệu chấm công của các nhân viên trong khoảng thời gian",
        "Tạo báo cáo kết quả cho bộ phận D2"
    ]
}}

Question: "So sánh đi muộn phòng nhân sự tháng này với tháng trước"
Output: {{
    "tasks": [
        "Xác định thông tin các nhân viên trong phòng nhân sự",
        "Trích xuất thời gian: tháng này",
        "Truy vấn dữ liệu chấm công của các nhân viên trong khoảng thời gian tháng này",
        "Tạo báo cáo kết quả cho phòng nhân sự trong tháng này",
        "Trích xuất thời gian: tháng trước",
        "Truy vấn dữ liệu chấm công của các nhân viên trong khoảng thời gian tháng trước",
        "Tạo báo cáo kết quả cho phòng nhân sự trong tháng trước",
        "Phân tích 2 báo cáo và đưa ra kết quả"
    ]
}}
"""

            response = await self.llm.process_message(state["message"], state["context"], prompt)
            result = json.loads(format_json_response(response))
            tasks = result.get("tasks", [])

            logger.info(f'Created {len(tasks)} tasks for question: {"\n".join(tasks)}')
            return tasks

        except Exception as e:
            logger.error(f"Task creation failed: {e}")
            return [
                "Phân tích câu hỏi của người dùng",
                "Xác định thông tin cần thiết",
                "Thực hiện truy vấn dữ liệu",
                "Tổng hợp và trả lời"
            ]

    @observe(name="plan_agent")
    async def create_plan(self, state: dict) -> List[Plan]:
        """
        Creates an execution plan using a prompt-driven approach.
        Selects agents based on QuestionAnalyzer's agent_names output.
        """
        analysis_list = await self.analyzer.analyze_question(state)

        # Store all intents in state for reference
        state["intents"] = [item["intent"] for item in analysis_list]
        state["scopes"] = [item["scope"] for item in analysis_list]
        state["entities_list"] = [item["entities"] for item in analysis_list]
        state["intent_plans"] = []
        state["intent_results"] = []
        plans = []
        for i, analysis in enumerate(analysis_list):
            plans.append(await self._create_plan_for_intent(analysis, i))
        return plans

    async def _create_plan_for_intent(self, analysis: dict, intent_index: int) -> Plan:
        """Create a plan for a single intent"""
        intent = analysis["intent"]
        scope = analysis["scope"]
        entities = analysis["entities"]
        agent_names = analysis.get("agent_names", [])

        steps, contingencies = self._generate_steps_for_intent(
            intent, scope, entities, agent_names, 1, intent_index
        )

        return Plan(
            steps=steps,
            contingencies=contingencies
        )

    def _generate_steps_for_intent(self, intent, scope, entities, agent_names, step_counter, intent_index):
        """Generate steps for a specific intent"""
        steps = []
        contingencies = {}
        
        if intent == "statistics":
            step_id = f"query{step_counter}"
            metric = entities.get("metric", "list_details")
            steps.append({
                "id": step_id,
                "action": "query_db",
                "attributes": {"metric": metric, "scope": scope, "intent_index": intent_index},
                "depends_on": []
            })
            contingencies[step_id] = [{
                "action": "ask_user",
                "prompt": "Không tìm thấy dữ liệu. Vui lòng kiểm tra lại."
            }]
            
        elif intent == "remain_leave":
            step_id = f"remain_leave{step_counter}"
            steps.append({
                "id": step_id,
                "action": "get_remain_leave",
                "attributes": {"scope": scope, "intent_index": intent_index},
                "depends_on": []
            })
            contingencies[step_id] = [{
                "action": "ask_user",
                "prompt": "Không thể lấy thông tin ngày phép còn lại."
            }]
            
        elif intent in ["timesheet", "payroll", "leave", "performance"]:
            depends_on = []
            
            # Add employee query step if needed
            if scope["type"] in ["individual", "department"]:
                query_step_id = f"query{step_counter}"
                steps.append({
                    "id": query_step_id,
                    "action": "query_db",
                    "attributes": {"metric": "employee_ids", "scope": scope, "intent_index": intent_index},
                    "depends_on": []
                })
                contingencies[query_step_id] = [{
                    "action": "ask_user",
                    "prompt": "Không tìm thấy nhân viên. Vui lòng kiểm tra lại."
                }]
                depends_on.append(query_step_id)
                step_counter += 1
            
            # Add time extraction step if needed
            if entities.get("time_period"):
                time_step_id = f"time{step_counter}"
                steps.append({
                    "id": time_step_id,
                    "action": "extract_time",
                    "attributes": {"period": entities["time_period"], "intent_index": intent_index},
                    "depends_on": []
                })
                contingencies[time_step_id] = [{
                    "action": "ask_user",
                    "prompt": "Thời gian không rõ. Vui lòng cung cấp thời gian cụ thể."
                }]
                depends_on.append(time_step_id)
                step_counter += 1
            
            # Add agent call steps
            if agent_names:
                for agent_name in agent_names:
                    agent_step_id = f"agent{step_counter}"
                    steps.append({
                        "id": agent_step_id,
                        "action": "call_agent",
                        "attributes": {"agent_name": agent_name, "intent": intent, "intent_index": intent_index},
                        "depends_on": depends_on
                    })
                    contingencies[agent_step_id] = [{
                        "action": "ask_user",
                        "prompt": f"Không thể xử lý yêu cầu về {intent}. Vui lòng cung cấp thêm chi tiết."
                    }]
                    step_counter += 1
            elif intent in ["timesheet", "leave"]:
                # Ensure timesheet agent is called even if agent_names is empty
                agent_step_id = f"agent{step_counter}"
                steps.append({
                    "id": agent_step_id,
                    "action": "call_agent",
                    "attributes": {"agent_name": "timesheet", "intent": intent, "intent_index": intent_index},
                    "depends_on": depends_on
                })
                contingencies[agent_step_id] = [{
                    "action": "ask_user",
                    "prompt": "Không thể xử lý yêu cầu về timesheet. Vui lòng cung cấp thêm chi tiết."
                }]
            else:
                # No agents available, ask for clarification
                clarify_step_id = f"clarify{step_counter}"
                steps.append({
                    "id": clarify_step_id,
                    "action": "ask_user",
                    "attributes": {"prompt": f"Không có thông tin về {intent}. Vui lòng cung cấp câu hỏi cụ thể hơn.", "intent_index": intent_index},
                    "depends_on": []
                })
                contingencies[clarify_step_id] = [{
                    "action": "ask_user",
                    "prompt": "Câu hỏi vẫn chưa rõ. Vui lòng cung cấp thêm chi tiết."
                }]
        
        return steps, contingencies
