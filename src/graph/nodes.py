import calendar
import json
import logging
import os
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Set, List

import pytz
from langfuse.decorators import observe

from src.agents.agent_internal.timesheet_agent.timesheet_agent import TimesheetAgent
from src.modules.router.subdomain_router import SubdomainRouter
from src.utils.utils import format_json_response, safe_get, process_department
from src.utils.time_extraction_api import calculate_years_worked
from .db_handler import DatabaseHandler, StatisticsProcessor
from .planning import PlanCreator
from .state import ChatbotState
from ..utils.constant import DEPARTMENT_2_ID

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
SYSTEM_PROMPT = os.getenv("SYSTEM_PROMPT")


class CoordinatorNodes:
    def __init__(self, config, domain: str, llm, domains_dir: str, function_caller, context_manager):
        self.llm = llm
        self.function_caller = function_caller
        self.context_manager = context_manager
        self.timeout = config.timeout
        self.domain = domain
        self.domains_dir = domains_dir
        self.subdomain_router = SubdomainRouter(self.llm, domains_dir)
        self.sub_agents = self._load_agents()
        self.assistant = PlanCreator(llm, self.sub_agents)
        self.db_handler = DatabaseHandler(llm)
        self.stats_processor = StatisticsProcessor(self.llm)
        self.subdomains = ["attendance", "payroll", "leave", "performance"]
        self.tools = self.get_list_tools_domain(self.subdomains)
        self.max_retries = config.max_retries

    def _load_agents(self) -> Dict[str, Dict[str, object]]:
        agents = {
            "attendance": {
                "timesheet": {
                    "instance": TimesheetAgent(self.function_caller, self.llm),
                    "description": "Handles queries about employee attendance, including late check-ins, early check-outs, absences, and leave days."
                }
            }
        }
        return agents

    # @observe()
    async def _process(self, state):
        state.setdefault("retry_count", {})
        state.setdefault("agent_results", {})
        state.setdefault("entities", {})
        state["entities"].setdefault("time_period", {})
        state.setdefault("context", self.context_manager.get_context(state["user_id"]))
        state.setdefault("attempts", 0)
        state.setdefault("dependencies", {})
        # state.setdefault("conversation_history", [])
        state.setdefault("employee_ids", [])

        # Get chat history from context
        chat_history = state["context"].get("chat_history", [])

        # Check if the message can be answered from chat history
            # history_check = await self._check_answerable_from_history(state)
            # if history_check.get("able_answer", False):
            #     state["response"] = history_check.get("answer", "")
            #     state["context"]["chat_history"].append({"content": state["message"], "role": "user"})
            #     state["context"]["chat_history"].append({"content": state["response"], "role": "assistant"})
            #     return state
        

        # Rewrite the message with context from chat history
        # if chat_history:
        original_message = state["message"]
        state["message"] = await self._rewrite_message_with_context(state["message"], chat_history)
        if original_message != state["message"]:
            logger.info(f"Rewrote message: '{original_message}' -> '{state['message']}'")
            # Store original message for reference
            state["original_message"] = original_message

        if await self._is_casual(state):
            state["response"] = await self._handle_casual(state)
            state["context"]["chat_history"].append({"content": state["message"], "role": "user"})
            state["context"]["chat_history"].append({"content": state["response"], "role": "assistant"})
            return state

        if not state.get("subdomains"):
            state = await self.subdomain_router.route_to_subdomains(state)
            if not state["subdomains"]:
                state["response"] = "Tôi không hiểu câu hỏi. Vui lòng cung cấp thông tin chi tiết hơn"
                state["context"]["chat_history"].append({"content": state["message"], "role": "user"})
                state["context"]["chat_history"].append({"content": state["response"], "role": "assistant"})
                return state

        while state["attempts"] < self.max_retries:
            if not state.get("plan"):
                plans = await self.assistant.create_plan(state)
                state["plans"] = plans
                state["current_plan_index"] = 0
                state["plan_results"] = []
            
            # Execute all plans sequentially
            while state["current_plan_index"] < len(state["plans"]):
                current_plan = state["plans"][state["current_plan_index"]]
                state["plan"] = current_plan  # Set current plan for compatibility
                
                await self._execute_plan(state)

                # Store results for this plan
                plan_result = {
                    "plan_index": state["current_plan_index"],
                    "intent": state["intents"][state["current_plan_index"]] if state["current_plan_index"] < len(state.get("intents", [])) else "unknown",
                    "agent_results": state.get("agent_results", {}).copy(),
                    "completed": current_plan.is_completed()
                }
                state["plan_results"].append(plan_result)

                # Move to next plan
                state["current_plan_index"] += 1
                
                # Reset agent_results for next plan
                state["agent_results"] = {}
            
            # All plans executed, combine results
            if state["plan_results"]:
                final_analysis = await self._combine_plan_results(state)
                state["response"] = final_analysis
                state["context"]["chat_history"].append({"content": state["message"], "role": "user"})
                state["context"]["chat_history"].append({"content": state["response"], "role": "assistant"})
                return state
            
            state["attempts"] += 1

    async def _combine_plan_results(self, state: ChatbotState) -> str:
        """Combine results from multiple executed plans into a single response."""
        plan_results = state.get("plan_results", [])
        
        if not plan_results:
            return "Tôi không thể xử lý yêu cầu. Vui lòng cung cấp chi tiết cụ thể hơn."
        
        # Collect all agent results from all plans
        combined_agent_results = {}
        intents_processed = []
        
        for plan_result in plan_results:
            intent = plan_result.get("intent", "unknown")
            intents_processed.append(intent)
            
            # Merge agent results with plan-specific prefixes
            for step_id, result in plan_result.get("agent_results", {}).items():
                combined_key = f"plan{plan_result['plan_index']}_{step_id}"
                combined_agent_results[combined_key] = result
        
        # Create a temporary state for analysis
        temp_state = state.copy()
        temp_state["agent_results"] = combined_agent_results
        temp_state["processed_intents"] = intents_processed
        
        prompt = (
            "You are an HR assistant analyzing results from multiple execution plans to answer a user query. "
            "Provide a comprehensive, user-friendly response in Vietnamese based on all the results.\n\n"
            f"Question: '{state['message']}'\n"
            f"Processed intents: {intents_processed}\n"
            f"Combined results from all plans:\n{json.dumps(combined_agent_results, ensure_ascii=False, indent=2)}\n\n"
            "Instructions:\n"
            "1. Analyze all results from different plans and intents\n"
            "2. Combine information logically and coherently\n"
            "3. Provide a detailed, professional response in Vietnamese\n"
            "4. If results are contradictory, prioritize the most relevant information\n"
            "5. Structure the response clearly with appropriate sections if needed\n"
        )
        
        try:
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            return response
        except Exception as e:
            logger.error(f"Error combining plan results: {str(e)}")
            # Fallback: use the first successful result
            for plan_result in plan_results:
                for result in plan_result.get("agent_results", {}).values():
                    if result.get("status") == "success" and result.get("response"):
                        return result["response"]
            return "Xin lỗi, không thể xử lý yêu cầu do lỗi hệ thống."

    @observe(name="execute_agent")
    async def _execute_plan(self, state: ChatbotState) -> None:
        executed_steps: Set[str] = set()
        current_plan = state.get("plan")  # Get current plan being executed
        if not current_plan:
            return
        while not current_plan.is_completed():
            current_step = current_plan.get_current_step()
            if current_step and await self._can_execute(current_step, executed_steps):
                result = await self._execute_step(state, current_step)
                if not result["success"]:
                    await self._handle_step_failure(state, current_step)
                    return
                if "id" not in current_step:
                    state["error_message"] = "Current step missing 'id' field"
                    return
                executed_steps.add(current_step["id"])
                if state.get("response"):
                    return
            current_plan.advance()

    # @observe(name="is_can_execute_tool")
    async def _can_execute(self, step: Dict[str, Any], executed_steps: Set[str]) -> bool:
        dependencies = step.get("depends_on", [])
        return all(dep in executed_steps for dep in dependencies)

    async def _handle_step_failure(self, state: ChatbotState, step: Dict[str, Any]) -> None:
        if "id" not in step:
            state["error_message"] = "Step missing 'id' field"
            return
        contingency = state["plan"].get_contingency(step["id"])
        if contingency:
            await self._execute_contingency(state, contingency)
        else:
            state["error_message"] = f"Step {step['id']} failed with no contingency."

    @observe(name="execute_step_tool")
    async def _execute_step(self, state, step):
        action = step["action"]
        try:
            if action == "query_db":
                metric = step["attributes"]["metric"]
                scope = step["attributes"]["scope"]
                function_data = {
                    "path": "/employee/list",
                    "method": "post",
                    "params": {}

                }

                # query, params = await self.db_handler.generate_sql_query(state, metric, scope)

                if scope.get("type") == 'individual':
                    function_data["params"]["query"] = {
                        "status": "working",
                        "name": f"{scope.get('value')}"
                    }
                elif scope.get("type") == "department":
                    department_id = DEPARTMENT_2_ID[f"{scope.get('value').lower()}"]
                    function_data["params"]["query"] = {
                        "status": "working",
                        "directory_id": {
                            "$in": [department_id]
                        }
                    }
                elif scope.get("type") == "company":
                    function_data["params"]["query"] = {
                        "status": "working"
                    }
                # result_data = self.db_handler.execute_query(query, params)
                result_data_ = await self.function_caller.execute(function_data)
                result_data = []
                for result_item in result_data_.get("data", []):
                    if result_item.get("employee_directories"):
                        result_data.append({
                            "name": result_item.get("name"),
                            "hr_code": result_item.get("hr_code"),
                            "employee_id": result_item.get("id"),
                            "email": result_item.get("email"),
                            "department": process_department(result_item['employee_directories']),
                            "employment_type": safe_get(result_item, "employment_type", "name"),
                            "start_date": result_item.get("start_date"),
                            "probationary_start_date": result_item.get("probationary_start_date"),
                            "official_start_date": result_item.get("official_start_date"),
                            "avg_years": calculate_years_worked(result_item.get("start_date"))
                        })
                if metric == "count":
                    result_data = [{"count": len(result_data)}]
                if 'call_agent' in [action['action'] for action in state['plan'].steps]:
                    employee_info = [
                        {"employee_id": str(emp["employee_id"]), "hr_code": emp["hr_code"], "name": emp["name"]}
                        for emp in result_data
                    ]
                    state["employee_ids"] = [emp["employee_id"] for emp in employee_info]
                    state["employee_names"] = [emp["name"] for emp in employee_info]
                    state["employee_info"] = employee_info

                    state["agent_results"][step["id"]] = {
                        "response": employee_info,
                        "status": "success",
                        "data": result_data,
                        "agent_name": "statistics"
                    }
                    return {"success": True}

                predefined_metrics = [
                    "count", "list_names", "list_details", "average_years",
                    "full_time_count", "department_list", "employee_ids"
                ]
                if metric in predefined_metrics:
                    result = self.stats_processor.process_statistics(result_data, metric, scope)
                    state["agent_results"][step["id"]] = {
                        "response": result["response"],
                        "status": "success",
                        "data": result["data"],
                        "agent_name": "statistics"
                    }
                    # state["response"] = result["response"]
                    state["response"] = await self._process_db_result(state, result, metric, scope)
                else:
                    result = {"response": None, "data": result_data}
                    state["agent_results"][step["id"]] = {
                        "response": None,
                        "status": "success",
                        "data": result_data,
                        "agent_name": "statistics"
                    }
                    state["response"] = await self._process_db_result(state, result, metric, scope)
                return {"success": True}
            elif action == "get_remain_leave":
                scope = step["attributes"]["scope"]
                function_data = {
                    "path": "/employee/find-one",
                    "method": "post",
                    "params": {
                        "query": {"name": f"{scope['value']}"},
                        "populates": [{
                            "name": "employee_time_off",
                            "query": {"year": datetime.now().year}
                        }]
                    }
                }
                result_data_ = await self.function_caller.execute(function_data)
                employee_time_off = result_data_["employee_time_off"]
                if not employee_time_off:
                    state[
                        "response"] = f"Không có thông tin về ngày nghỉ phép của {scope['value']} trong năm {datetime.now().year}"
                    return {"success": True}
                processed_leave = self.process_remain_leave_result(employee_time_off)
                processed_leave = f"""Thống kê nghỉ phép năm {datetime.now().year}:
    {processed_leave}"""
                state["response"] = await self._analyze_leave_tardiness(state, processed_leave)
                return {"success": True}
            elif action == "get_employee_ids":
                scope = step["attributes"]["scope"]
                query, params = await self.db_handler.generate_sql_query(state, "employee_ids", scope)
                employees = self.db_handler.execute_query(query, params)
                state["employee_ids"] = [str(emp["employee_id"]) for emp in employees]
                state["agent_results"][step["id"]] = {
                    "response": f"Found {len(employees)} employees",
                    "status": "success",
                    "data": {"employee_ids": state["employee_ids"]},
                    "agent_name": "get_employee_ids"
                }
                return {"success": bool(state["employee_ids"])}
            elif action == "extract_time":
                state = await self._extract_time(state)
                success = bool(state["entities"].get("time_period"))
                if success:
                    state["agent_results"][step["id"]] = {
                        "response": f"Time period: {state['entities']['time_period']}",
                        "status": "success",
                        "data": state["entities"]["time_period"],
                        "agent_name": "extract_time"
                    }
                else:
                    state["agent_results"][step["id"]] = {
                        "response": "Failed to extract time period",
                        "status": "error",
                        "data": {},
                        "agent_name": "extract_time"
                    }
                return {"success": success}
            elif action == "call_agent":
                agent_name = step["attributes"]["agent_name"]
                step_id = step["id"]
                await self._execute_agent(state, agent_name, step_id)
                success = state["agent_results"].get(step_id, {}).get("status") == "success"
                return {"success": success}
            else:
                raise ValueError(f"Unknown action: {action}")
        except Exception as e:
            state["error_message"] = f"Error in step {step['id']}: {e}"
            state["agent_results"][step["id"]] = {
                "response": str(e),
                "status": "error",
                "data": {},
                "agent_name": action
            }
            return {"success": False}

    async def _execute_contingency(self, state: ChatbotState, contingency: List[Dict[str, Any]]) -> None:
        for action in contingency:
            if action["action"] == "ask_user":
                state["response"] = action["prompt"]
                break

    @observe(name="check_history_answerable")
    async def _check_answerable_from_history(self, state: ChatbotState) -> Dict[str, Any]:
        """Check if the current message can be answered from chat history."""
        chat_history = state["context"].get("chat_history", [])
        
        # If no chat history, cannot answer from history
        if not chat_history:
            return {"able_answer": False, "answer": ""}
        
        # Format chat history for the prompt
        history_str = ""
        for entry in chat_history:
            role = entry.get("role", "")
            content = entry.get("content", "")
            if role == "user":
                history_str += f"User: {content}\n"
            elif role == "assistant":
                history_str += f"Assistant: {content}\n"
        
        prompt = f"""You are given a conversation history and a current message. Your task is to determine whether the current message can be answered solely based on the information provided in the conversation history.

Conversation history: 
{history_str}

Current message: {state["message"]}

Please follow this strict format for your response:

If the conversation history provides enough information to answer the current message, return:
{{
  "able_answer": true,
  "answer": "..." // replace with your actual answer in Vietnamese
}}

If the conversation history does NOT provide enough information, return:
{{
  "able_answer": false,
  "answer": ""
}}

Criteria for able_answer = true:
- The question refers to details already mentioned or clarified in the history
- The user repeats or rephrases a previous query that was already addressed
- The information needed to answer is fully contained in the previous messages
- Follow-up questions that can be answered using context from previous exchanges

Criteria for able_answer = false:
- The user asks a new question that introduces unknown context
- Required information is missing or ambiguous in the history
- The answer requires assumptions or external knowledge not covered in the previous messages
- Questions about new employees, dates, or data not mentioned before

Your only output should be the appropriate JSON object."""

        try:
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            result = json.loads(format_json_response(response))
            return result
        except Exception as e:
            logger.error(f"Error checking if answerable from history: {str(e)}")
            return {"able_answer": False, "answer": ""}

    @observe(name="check_casual_question_tool")
    async def _is_casual(self, state: ChatbotState) -> bool:
        casual_prompt = (
            f"{SYSTEM_PROMPT}."
            "You have access to the chat history, including previous interactions with the user.\n\n"
            f"Current question: '{state['message']}'\n\n"
            "When classifying the current question, consider the chat history to understand the context and intent behind the question.\n"
            "Instructions:\n"
            "1. Determine if the current question is a casual greeting (e.g., 'xin chào', 'hello') or unrelated to internal company data (e.g., 'how's the weather?', 'tell me a joke') when considering the chat history.\n"
            "2. If it is casual or unrelated based on the entire conversation context, respond with exactly 'CASUAL' (nothing else).\n"
            "3. If it relates to internal company data (e.g., tardiness, timesheet, employee info), respond with exactly 'DATA_QUERY' (nothing else).\n"
            "Be very precise in your classification.\n\n"
            "Examples:\n"
            "Example 1:\n"
            "Chat history:\n"
            "- User: Hello, chatbot!\n"
            "Current question: I want to get Tran Tung Duong's timesheet in March 2025.\n"
            "Classification: DATA_QUERY\n"
            "Explanation: Although the first message was a greeting (CASUAL), the current question relates to internal company information, so it is DATA_QUERY.\n\n"
            "Example 2:\n"
            "Chat history:\n"
            "- User: Do you know any good jokes?\n"
            "- User: Yeah, tell me one.\n"
            "Current question: By the way, how do I submit my timesheet?\n"
            "Classification: DATA_QUERY\n"
            "Explanation: Although previous messages were casual (CASUAL), the current question relates to internal company information, so it is DATA_QUERY.\n\n"
            "Example 3:\n"
            "Chat history:\n"
            "- User: I need help with my tardiness record.\n"
            "Current question: Thanks for that. By the way, do you know any good restaurants nearby?\n"
            "Classification: CASUAL\n"
            "Explanation: The current question is not related to internal company information and is unrelated (CASUAL), even though the previous question was DATA_QUERY."
        )
        response = await self.llm.process_message(state["message"], {}, casual_prompt)
        return "CASUAL" in response.strip().upper()

    @observe(name="chat_casual_tool")
    async def _handle_casual(self, state: ChatbotState) -> str:
        casual_response_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "This has been classified as a casual interaction. Provide a friendly, conversational response.\n"
            "If the user greeted in Vietnamese, respond in Vietnamese. Otherwise respond in English.\n"
            "Keep it brief, friendly and natural."
        )
        return await self.llm.process_message(state["message"], state["context"], casual_response_prompt)

    @observe(name="extract_time_tool")
    async def _extract_time(self, state: 'ChatbotState') -> 'ChatbotState':
        vietnam_tz = pytz.timezone('Asia/Ho_Chi_Minh')
        current_date = datetime.now(vietnam_tz)
        period = state["plan"].get_current_step()["attributes"]["period"]

        def get_days_in_month(year: int, month: int) -> List[str]:
            days_in_month = (
                31, 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28, 31, 30, 31, 30, 31, 31, 30,
                31,
                30, 31)
            return [f"{year}-{month:02d}-{day:02d}" for day in range(1, days_in_month[month - 1] + 1)]

        def group_days_by_month(days: List[str]) -> Dict[str, List[str]]:
            """Group a list of dates into a dictionary by month."""
            result = {}
            for day in sorted(days):
                year, month, _ = day.split('-')
                month_key = f"tháng {int(month)}"
                if month_key not in result:
                    result[month_key] = []
                result[month_key].append(day)
            return result

        def parse_specific_days_or_months(period: str, current_date: datetime) -> Dict[str, List[str]]:
            year = current_date.year
            result_days = set()

            # Handle specific days (e.g., "ngày 10 và ngày 20")
            day_matches = re.findall(r"ngày\s*(\d{1,2})(?:\s*và\s*ngày\s*(\d{1,2}))?", period, re.IGNORECASE)
            for match in day_matches:
                day1 = int(match[0])
                if match[1]:
                    day2 = int(match[1])
                    for day in [day1, day2]:
                        if 1 <= day <= 31:
                            result_days.add(f"{year}-{current_date.month:02d}-{day:02d}")
                else:
                    if 1 <= day1 <= 31:
                        result_days.add(f"{year}-{current_date.month:02d}-{day1:02d}")

            # Handle specific months (e.g., "tháng 1 và tháng 3")
            month_matches = re.findall(r"tháng\s*(\d{1,2})(?:\s*và\s*tháng\s*(\d{1,2}))?", period, re.IGNORECASE)
            for match in month_matches:
                month1 = int(match[0])
                if 1 <= month1 <= 12:
                    result_days.update(get_days_in_month(year, month1))
                if match[1]:
                    month2 = int(match[1])
                    if 1 <= month2 <= 12:
                        result_days.update(get_days_in_month(year, month2))

            # Handle quarters (e.g., "quý 1", "quý 2")
            quarter_matches = re.findall(r"quý\s*(\d)", period, re.IGNORECASE)
            for quarter in quarter_matches:
                quarter = int(quarter)
                if 1 <= quarter <= 4:
                    start_month = (quarter - 1) * 3 + 1
                    for month in range(start_month, start_month + 3):
                        result_days.update(get_days_in_month(year, month))

            # Handle years (e.g., "năm 2023")
            year_matches = re.findall(r"năm\s*(\d{4})", period, re.IGNORECASE)
            for year_str in year_matches:
                year = int(year_str)
                for month in range(1, 13):
                    result_days.update(get_days_in_month(year, month))

            return group_days_by_month(list(result_days)) if result_days else {}

        # Try parsing specific days, months, quarters, or years first
        # date_dict = parse_specific_days_or_months(period, current_date)
        # if date_dict:
        #     state["entities"]["time_period"][period] = date_dict
        #     return state

        formatted_date = current_date.strftime('%A, %Y-%m-%d')
        weekday = current_date.weekday()
        last_monday = (current_date - timedelta(days=weekday + 7)).strftime('%A, %Y-%m-%d')
        last_sunday = (current_date - timedelta(days=weekday + 1)).strftime('%A, %Y-%m-%d')
        this_monday = (current_date - timedelta(days=weekday)).strftime('%A, %Y-%m-%d')
        this_sunday = (current_date + timedelta(days=(6 - weekday))).strftime('%A, %Y-%m-%d')
        next_monday = (current_date - timedelta(days=weekday) + timedelta(days=7)).strftime('%A, %Y-%m-%d')
        next_sunday = (current_date + timedelta(days=(6 - weekday)) + timedelta(days=7)).strftime('%A, %Y-%m-%d')

        # Fallback to LLM-based parsing for complex or ambiguous periods
        prompt = f"""You are a Vietnamese time parser. Extract precise "start_date" and "end_date" in "YYYY-MM-DD" format based on the input, or specific days/months if mentioned.

Current date: "{formatted_date}"
Input: "{period}"

Return JSON only:
- Valid: {{"start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD"}} (for ranges)
- Specific days: {{"days": ["YYYY-MM-DD", ...]}} (for specific days like "ngày 10 và 20")
- Specific months: {{"months": [1, 3, ...]}} (for specific months like "tháng 1 và tháng 3")
- Invalid: {{"start_date": null, "end_date": null, "error": "unclear"}}

Vietnamese expressions (ALWAYS considered valid unless marked ambiguous):
- Day:
  - "hôm nay" → today = "{formatted_date}"
  - "hôm qua" → "{formatted_date}" - 1 day
  - "ngày mai" → "{formatted_date}" + 1 day
  - "ngày X và ngày Y" → specific days X and Y in current month
-Week: A week is always from **Monday to Sunday**. For example, if today is "{formatted_date}", then:
  - "tuần này " → full current week (from "{this_monday}" to "{this_sunday}")
  - "tuần trước" → previous week (from "{last_monday}" to "{last_sunday}")
  - "tuần sau" → next week (from "{next_monday}" to "{next_sunday}")
  - "tuần X và tuần Y" → specific week X and Y in current month
- Month:
  - "tháng này" → full current month
  - "tháng trước" → previous month
  - "tháng sau" → next month
  - "tháng X" → month X in current year (if X > current month, use last year)
  - "tháng X và tháng Y" → specific months X and Y
  - "tháng X năm YYYY" → month X in year YYYY
- Year:
  - "năm nay" → Jan 1 to Dec 31 of current year
  - "năm trước" → Jan 1 to Dec 31 of last year
  - "năm sau" → Jan 1 to Dec 31 of next year
  - "năm YYYY" → full year YYYY

Rules:
- Always use accurate number of days per month (28/29/30/31)
- Leap year: Feb = 29 days if divisible by 4 (except century years not divisible by 400)
- Ambiguous examples (return error): "mấy ngày trước", "tuần tới", "gần đây", etc.
- For specific days/months, return only those explicitly mentioned
- Output: JSON only, no explanation
"""

        try:
            # response = await self.llm.process_message(state["message"], state["context"], prompt)
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            # cleaned = response.strip().replace("```json", "").replace("```", "").strip()
            # if cleaned.startswith("{{") and cleaned.endswith("}}"):
            #     cleaned = cleaned[1:-1]
            # elif cleaned.startswith("{") and cleaned.endswith("}"):
            #     pass
            # else:
            #     cleaned = f"{{{cleaned}}}"
            # time_period = json.loads(cleaned.strip())
            time_period = json.loads(format_json_response(response))

            result_days = set()
            if "days" in time_period:
                result_days.update(time_period["days"])
            elif "months" in time_period:
                for month in time_period["months"]:
                    if 1 <= month <= 12:
                        result_days.update(get_days_in_month(current_date.year, month))
            elif "quarters" in time_period:
                for quarter in time_period["quarters"]:
                    if 1 <= quarter <= 4:
                        start_month = (quarter - 1) * 3 + 1
                        for month in range(start_month, start_month + 3):
                            result_days.update(get_days_in_month(current_date.year, month))
            elif "years" in time_period:
                for year in time_period["years"]:
                    for month in range(1, 13):
                        result_days.update(get_days_in_month(year, month))
            elif "start_date" in time_period and "end_date" in time_period:
                start_date = time_period.get("start_date")
                end_date = time_period.get("end_date")
                if not (start_date and end_date):
                    state[
                        "response"] = f"Không thể xác định thời gian '{period}'. Vui lòng cung cấp rõ hơn (ví dụ: {current_date.strftime('%Y-%m-%d')})."
                    return state
                try:
                    start = datetime.strptime(start_date, "%Y-%m-%d")
                    end = datetime.strptime(end_date, "%Y-%m-%d")
                    delta = (end - start).days + 1
                    for i in range(delta):
                        day = start + timedelta(days=i)
                        result_days.add(day.strftime("%Y-%m-%d"))
                except ValueError:
                    state[
                        "response"] = f"Thời gian '{period}' không hợp lệ. Vui lòng cung cấp rõ hơn (ví dụ: 'tháng 4/2025')."
                    return state
            else:
                state[
                    "response"] = f"Không thể xác định thời gian '{period}'. Vui lòng cung cấp rõ hơn (ví dụ: {current_date.strftime('%Y-%m-%d')})."
                return state

            state["entities"]["time_period"][period] = group_days_by_month(list(result_days))
            return state
        except Exception as e:
            logger.error(f"Error extracting time: {str(e)}")
            state["response"] = f"Lỗi xử lý thời gian '{period}'. Vui lòng cung cấp rõ hơn (ví dụ: 'ngày 8/4/2025')."
            return state

    # @observe()
    async def _execute_agent(self, state: ChatbotState, agent_name: str, step_id: str) -> None:
        subdomain = next((s for s in self.sub_agents if agent_name in self.sub_agents[s]), state["subdomains"][0])
        agent = self.sub_agents.get(subdomain, {}).get(agent_name).get('instance')
        if not agent:
            state["agent_results"][step_id] = {"response": None, "status": "error",
                                               "error": f"Agent {agent_name} not found"}
            return
        try:
            result = await agent.execute(state, self.tools, dependencies=state["dependencies"])
            state["agent_results"][step_id] = {
                "response": result.get("response"),
                "status": "success",
                "error": None,
                "data": result.get("data"),
                "agent_name": agent_name
            }
            # state["response"] = result["data"]
        except Exception as e:
            state["agent_results"][step_id] = {
                "response": None,
                "status": "error",
                "error": str(e),
                "data": None,
                "agent_name": agent_name
            }

    async def _analyze_results(self, state: ChatbotState) -> Dict:
        tmp = state.copy()
        del tmp["agent_results"]["query1"]["data"]
        del tmp["agent_results"]["time1"]["data"]
        del tmp["agent_results"]["agent1"]["data"]
        prompt = (
            "You have access to the chat history, including previous interactions with the user. Consider it to understand the context and intent behind the question.\n"
            f"Question: '{state['message']}'\n"
            f"Current results: {json.dumps(tmp['agent_results'], ensure_ascii=False, indent=2)}\n"
            f"Entities extracted: {json.dumps(tmp['entities'], ensure_ascii=False, indent=2)}\n"
            f"Execution plan: {json.dumps({'steps': tmp['plan'].steps}, ensure_ascii=False, indent=2)}\n"
            "Instructions:\n"
            "You are an HR assistant analyzing the results of an execution plan to answer a user query about employee timesheet data. Provide a detailed, user-friendly response in Vietnamese, tailored for HR personnel or employees, based on the analysis below.\n\n"
            "Analyze:\n"
            "1. Are the results sufficient to answer the question? (yes/no)\n"
            "   - Check if 'agent_results' contains all required data based on the question, entities, and plan steps.\n"
            "   - Each result in 'agent_results' is stored by step ID (e.g., 'agent1', 'agent2') with fields: 'agent_name', 'response', 'status', and 'error'.\n"
            "   - Required data depends on the question and entities (e.g., employee_id, time_period, specific metrics like late minutes).\n"
            "   - Consider entities like 'employee_name', 'time_period' (start_date, end_date, specific_days), and 'metrics' (e.g., đi muộn, nghỉ phép).\n"
            "   - A result is sufficient if all plan steps have 'status': 'success' and the 'response' field contains relevant data for the question.\n\n"
            "2. If sufficient (is_complete: true):\n"
            "   - Combine results from successful steps into a cohesive, professional response in Vietnamese.\n"
            "   - Use the 'response' field from each step, linking to step IDs for clarity (e.g., 'theo agent1').\n"
            "   - Incorporate context from 'entities' (e.g., employee names, time periods) to make the response specific.\n"
            "   - Structure the response with clear sections (e.g., 'Tổng hợp', 'Chi tiết') and use a professional tone.\n"
            "   - Example: 'Ngô Gia Phong (Mã NV: NV001) đi muộn 40 phút vào ngày 2025-04-01, xin đi muộn 30 phút, số phút đi muộn thực tế là 10 phút, check-in lúc 08:30 (theo agent1).\nTổng cộng 2 lần đi muộn trong tháng 4 (theo agent2).'\n\n"
            "3. If insufficient (is_complete: false):\n"
            "   - Identify missing or erroneous data:\n"
            "     - Missing data: Specify which step ID lacks results (e.g., 'no result for agent3') or which entity is missing (e.g., 'employee_id not provided').\n"
            "     - Errors: For steps with 'status': 'error', report the 'error' field and step ID (e.g., 'agent2 failed: invalid date format').\n"
            "   - Provide a clear explanation in Vietnamese of what’s wrong and why it prevents answering the question.\n"
            "   - Example: 'Thiếu dữ liệu từ agent3 (thông tin chấm công). Agent2 báo lỗi: định dạng ngày không hợp lệ.'\n\n"
            "4. Suggest next steps if needed:\n"
            "   - For missing data, suggest actions like 'get_employee_id', 'call_agent:tardiness with period=tháng 3'.\n"
            "   - For errors, suggest retries or corrections, e.g., 'retry_agent:agent2 with corrected date format'.\n"
            "   - Be specific, referencing step IDs and entities (e.g., 'call_agent:timesheet with employee_id=NV001').\n"
            "   - If results are sufficient, set 'next_steps' to an empty list.\n\n"
            "Output: Return JSON object (if JSON output has \', change it to \"):\n"
            "```json\n"
            "{\n"
            "  'response': str,  // Detailed response in Vietnamese, summarizing results or explaining issues\n"
            "  'is_complete': bool,  // True if results are sufficient, False otherwise\n"
            "  'next_steps': [str]  // List of suggested actions (empty if complete)\n"
            "}\n"
            "```\n\n"
            "Example Output:\n"
            "Example 1:\n"
            "```json\n"
            "{\n"
            "  'response': 'Ngô Gia Phong (Mã NV: NV001) đi muộn 40 phút vào ngày 2025-04-01, xin đi muộn 30 phút, số phút đi muộn thực tế là 10 phút, check-in lúc 08:30 (theo agent1).\nTổng cộng 2 lần đi muộn trong tháng 4 (theo agent2).',\n"
            "  'is_complete': true,\n"
            "  'next_steps': []\n"
            "}\n"
            "```\n\n"
            "Example 2:\n"
            "```json\n"
            "{\n"
            "  'response': 'Không thể trả lời đầy đủ do thiếu dữ liệu từ agent3 (thông tin chấm công). Agent2 báo lỗi: định dạng ngày không hợp lệ. Vui lòng cung cấp thêm thông tin hoặc thử lại.',\n"
            "  'is_complete': false,\n"
            "  'next_steps': ['get_employee_id', 'retry_agent:agent2 with corrected date format']\n"
            "}\n"
            "```"
        )
        try:
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            if "is_complete" not in response:
                return {"response": response, "is_complete": True, "next_steps": []}

            return json.loads(format_json_response(response))
        except Exception as e:
            logger.error(f"Final analysis failed: {str(e)}")
            return {"response": "Error in final analysis", "is_complete": False, "next_steps": []}

    async def _analyze_late_tardiness(self, state: ChatbotState) -> str:
        """Generate detailed analysis for late tardiness queries."""
        # if "không đi muộn ngày nào" in state.get('agent_results', {}).get('agent1', {}).get('response', ''):
        #     return state.get('agent_results', {}).get('agent1', {}).get('response', '')
        prompt = (
            f"Question: '{state['message']}'\n"
            f"Current results: {state.get('agent_results', {}).get('agent1', {}).get('response', '')}\n"
            "Instructions:\n"
            "You are an HR assistant analyzing late tardiness data. Provide a detailed response in Vietnamese following these guidelines:\n\n"
            "If there are no tardiness incidents, return the following sentence:\n"
            "   Nhân viên <employee name> không đi muộn ngày nào\n"
            "If tardiness cases are present, you should:\n"
            "1. Always include complete tardiness details for EACH instance, regardless of what the user asked:\n"
            "   - Raw minutes late (before any deductions)\n"
            "   - Minutes deducted by approved requests/letters (if applicable)\n"
            "   - Net minutes late (after deductions)\n"
            "   - Block count (both before and after deductions)\n"
            "   - Check-in time and location\n"
            "2. Format your response as follows:\n"
            "   - Begin with a brief summary showing:"
            "       - total tardiness instances\n"
            "       - total raw minutes late (block count)\n"
            "       - minutes deducted\n"
            "       - net minutes late (block count)\n"
            "   - List ALL tardiness instances chronologically with full details\n"
            "   - For each instance, clearly indicate whether there were deductions from approved requests"
            "   - Use the consistent format shown below for all entries, even when some have no deductions\n\n"
            "3. Format for each tardiness instance:\n"
            "   ```\n"
            "   Ngày YYYY-MM-DD:\n"
            "   - Phút đi muộn: X phút (Y block)\n"
            "   - Phút đi muộn trừ bởi đơn: Z phút (if applicable)\n"
            "   - Phút đi muộn sau khi trừ: X-Z phút (W block) (if applicable)\n"
            "   - Thời gian check-in: HH:MM:SS\n"
            "   - Địa điểm check-in: Location\n"
            "   ```\n\n"
            "4. Important rules:\n"
            "   - Use direct names without Vietnamese honorifics (use 'Nguyễn Văn A' not 'anh Nguyễn Văn A')\n"
            "   - Apply this detailed format even for simple questions like 'How many times was X late?'\n"
            "   - If no tardiness instances exist, clearly state this fact\n"
            "   - Respond in natural, professional Vietnamese\n\n"
            "This format must be applied for ALL individual tardiness queries, regardless of how simple the question is."
        )

        try:
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            return response
        except Exception as e:
            logger.error(f"Error in analyze_individual_tardiness: {str(e)}")
            return "Xin lỗi, không thể phân tích kết quả đi muộn cá nhân do lỗi hệ thống."

    async def _analyze_leave_tardiness(self, state: ChatbotState, statistic: str = "") -> str:
        """Generate concise analysis for leave tardiness queries."""
        # if "không nghỉ ngày nào" in state.get('agent_results', {}).get('agent1', {}).get('response', ''):
        #     return state.get('agent_results', {}).get('agent1', {}).get('response', '')
        prompt = (
            "You are an HR assistant responsible for analyzing employee-related data and providing insightful, "
            "professional responses.\n\n"
            "You will be given:\n"
            "- A question.\n"
            "- A set of current results related to that question.\n\n"
            "Instructions:\n"
            "- Analyze the data in the 'Current results' section only. Do not make assumptions or bring in any external"
            " knowledge.\n"
            "- Ignore any time-related information.\n"
            "- Respond in clear, natural, and professional **Vietnamese**.\n"
            "- Write your answer as **detailed** as possible.\n"
            # " (if relevant), but stay strictly within the boundaries of the provided data.\n"
            "- Avoid speculation or vague language. Focus on precision and clarity.\n\n"
            f"Question: '{state['message']}'\n"
            f"Current results:\n"
            f"{state.get('agent_results', {}).get('agent1', {}).get('response', '') if not statistic else statistic}\n"
        )

        try:
            response = await self.llm.process_message(state["message"], {"chat_history": []}, prompt)
            return response
        except Exception as e:
            logger.error(f"Error in analyze_individual_tardiness: {str(e)}")
            return "Xin lỗi, không thể phân tích kết quả đi muộn cá nhân do lỗi hệ thống."

    @observe(name="analyze_tool")
    async def _analyze_results_2(self, state: ChatbotState) -> str:
        """Analyze results and route to appropriate analysis function based on query type."""

        if state["scope"]["type"] == "individual":
            if state["intent"] == "leave":
                return await self._analyze_leave_tardiness(state)
            elif state["intent"] == "timesheet":
                if any(item in state["message"].lower() for item in ["muộn", "trễ", "sớm"]):
                    return await self._analyze_late_tardiness(state)
                return await self._analyze_leave_tardiness(state)
            else:
                return await self._analyze_leave_tardiness(state)
        else:
            return await self._analyze_leave_tardiness(state)

    def get_list_tools_domain(self, subdomains: list) -> Dict:
        tools = {}
        for subdomain in subdomains:
            tools[subdomain] = []
            apis_subdomain_dir = os.path.join(self.domains_dir, self.domain, subdomain, "apis")
            if os.path.isdir(apis_subdomain_dir):
                for api_domain in os.listdir(apis_subdomain_dir):
                    with open(os.path.join(apis_subdomain_dir, api_domain), "r") as f:
                        data_api = json.load(f)
                    tools[subdomain].append(data_api)
        return tools

    @observe(name="analyze_tool")
    async def _process_db_result(self, state, result, metric, scope):
        """Process database query results and generate appropriate response."""
        # For department scope, return the query response directly
        # if scope['type'] == 'department':
        #     if 'query1' in state['agent_results'] and 'response' in state['agent_results']['query1']:
        #         return state['agent_results']['query1']['response']
        return await self._generate_db_result_response(state, result, metric, scope)

    async def _generate_db_result_response(self, state, result, metric, scope):
        prompt_2 = (
            "You are an HR assistant responsible for analyzing employee-related data and providing insightful, "
            "professional responses.\n\n"
            "You will be given:\n"
            "- A question.\n"
            "- A set of current results related to that question.\n\n"
            "Instructions:\n"
            "- Analyze the data in the 'Current results' section only. Do not make assumptions or bring in any external"
            " knowledge.\n"
            "- Ignore any time-related information.\n"
            "- Respond in clear, natural, and professional **Vietnamese**.\n"
            "- Write your answer as **detailed** as possible.\n"
            "- Avoid speculation or vague language. Focus on precision and clarity.\n\n"
            f"Question: '{state['message']}'\n"
            f"Current results:\n"
            f"{result['data']}\n"
        )
        try:
            return await self.llm.process_message(state["message"], {"chat_history": []}, prompt_2)
        except Exception as e:
            logger.error(f"LLM response error: {e}")
            return result["response"]

    @observe(name="enhance_prompt_tool")
    async def _rewrite_message_with_context(self, message: str, chat_history: List[Dict]) -> str:
        """
        Rewrite the user message by incorporating relevant context from chat history.

        Args:
            message: The current user message
            chat_history: List of previous messages in format [{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]

        Returns:
            Enhanced message with contextual information
        """
        if not chat_history:
            return message

        # Limit history to last few interactions for context
        recent_history = chat_history[-3:] if len(chat_history) > 3 else chat_history

        # Format chat history for the prompt
        history_str = ""
        for entry in recent_history:
            role = entry.get("role", "")
            content = entry.get("content", "")
            if role == "user":
                history_str += f"User: {content}\n"

        prompt = (
            f"Here is the conversation history:\n"
            f"{history_str}\n"
            f"Current user message: '{message}'\n\n"
            f"Task:\n"
            f"If the current user message is already a clear and specific question that is understandable "
            f"on its own, return it unchanged.\n\n"
            f"Otherwise, rewrite the current user message as a clear and specific question, using relevant context "
            f"and details from the previous conversation. Ensure the rewritten question is:\n"
            f"- Self-contained (can be understood without referring back to the chat history)\n"
            f"- Concise and precise\n"
            f"- Reflecting the most likely intended meaning of the original message\n\n"
            f"Return only the rewritten or original question (no explanation).\n\n"
            f"Examples:\n"
            f"- Chat history:\n"
            f"  User: Nguyễn Tiến Anh đã nghỉ phép những ngày nào trong tháng 5?\n"
            f"  Current message: 'Tháng 6 thì sao?'\n"
            f"  Rewritten message: 'Tháng 6 Nguyễn Tiến Anh đã nghỉ phép những ngày nào?'\n"
            f"- Chat history:\n"
            f"  User: Thông tin về nhân sự Nguyễn Tiến Anh\n"
            f"  Current message: 'Tháng này anh ấy đi muộn bao nhiêu lần?'\n"
            f"  Rewritten message: 'Tháng này Nguyễn Tiến Anh đi muộn bao nhiêu lần?'\n"
            f"- Chat history:\n"
            f"  User: Tháng này Nguyễn Tiến Anh đi muộn bao nhiêu lần?\n"
            f"  Current message: 'Nguyễn Công Trí thì sao?'\n"
            f"  Rewritten message: 'Tháng này Nguyễn Công Trí đi muộn bao nhiêu lần?'\n"
            f"- Chat history:\n"
            f"  User: chi tiết chấm công của Nguyễn Tiến Anh vào thứ 2 tuần trước\n"
            f"  Current message: 'Hôm qua thì sao'\n"
            f"  Rewritten message: 'chi tiết chấm công của Nguyễn Tiến Anh hôm qua'"
        )

        try:
            rewritten_message = await self.llm.process_message(message, {"chat_history": []}, prompt)
            logger.info(f"Rewrote message: '{message}' -> '{rewritten_message}'")
            return rewritten_message.strip()
        except Exception as e:
            logger.error(f"Error rewriting message: {str(e)}")
            return message

    def process_remain_leave_result(self, leave_data: dict):
        total_leave = (leave_data["annual_year_detail"]["total"] + leave_data["accumulated_annual_year_detail"]["total"]
                       + leave_data["seniority_detail"]["total"])
        used_leave = (leave_data["annual_year_detail"]["used"] + leave_data["accumulated_annual_year_detail"]["used"]
                      + leave_data["seniority_detail"]["used"])
        expiry_leave = (
                    leave_data["annual_year_detail"]["expiry"] + leave_data["accumulated_annual_year_detail"]["expiry"]
                    + leave_data["seniority_detail"]["expiry"])
        return f"""Tổng số ngày phép trong năm: {total_leave} ngày
    Phép lũy kế: {leave_data["accumulated_annual_year_detail"]["total"]} (đã hết hạn)
    Phép năm: {leave_data["annual_year_detail"]["total"]}
    Phép thâm niên: {leave_data["seniority_detail"]["total"]}
Số ngày phép đã sử dụng: {used_leave} ngày
Số ngày phép đã hết hạn: {expiry_leave} ngày
Số ngày phép còn lại: {total_leave - used_leave - expiry_leave} ngày"""
