from typing import Dict, Any, Optional, Union, List
from pydantic import BaseModel

from src.llm_providers.llm_provider import LLMProvider
from src.modules.function_caller import FunctionCaller
from src.modules.context_manager import ContextManager
from src.modules.cache import CacheManager
from src.graph.graph import ChatbotGraph
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChatbotConfig(BaseModel):
    llm_type: Union[str, Dict[str, str]] = {"paid": "openai", "free": "openrouter"}
    api_base_url: str = "https://api.weekly.vn"
    max_context_length: int = 5
    timeout: int = 30
    human_in_loop_threshold: int = 5,
    max_retries: int = 5,
    rag_docs_path: str = "internal_docs"
    db_uri: str = "sqlite:///db.sqlite"
    max_chat_history: int = 10


class ChatbotCore:
    def __init__(self, config: ChatbotConfig, domain: str = "hr", domains_dir: str = "src/domains"):
        """Initialize Chatbot Core with configuration and graph."""
        self.config = config
        self.llm_provider = LLMProvider(provider=config.llm_type)
        self.function_caller = FunctionCaller(base_url=config.api_base_url, db_uri=config.db_uri)
        self.context_manager = ContextManager(max_length=config.max_context_length)
        self.cache_manager = CacheManager(ttl=3600)
        self.graph = ChatbotGraph(config, domain=domain, llm=self.llm_provider, domains_dir=domains_dir,
                                  function_caller=self.function_caller, context_manager=self.context_manager)
        self.timeout = config.timeout
        self.chat_histories = {}  # Store chat histories by user_id

    # @observe()
    async def process_request(self, message: str, user_id: str) -> str:
        """
        Process user request using ChatbotGraph with chat history.
        
        Args:
            message: The user message
            user_id: The user ID
            
        Returns:
            The response message
        """
        # Get or initialize chat history for this user
        if user_id not in self.chat_histories:
            self.chat_histories[user_id] = []

        # Get chat history for this user
        chat_history = self.chat_histories[user_id]



        # Update context manager with chat history
        # context = self.context_manager.get_context(user_id)

        # Convert chat history to the format expected by context manager
        for entry in chat_history:
            if entry["role"] == "user":
                # Find the corresponding assistant response
                for i, next_entry in enumerate(chat_history[chat_history.index(entry) + 1:], 1):
                    if next_entry["role"] == "assistant":
                        # Update context with this interaction
                        self.context_manager.update_context(
                            user_id=user_id,
                            message=entry["content"],
                            response=next_entry["content"]
                        )
                        break
                # Update context with user messages only
                # self.context_manager.update_context(
                #     user_id=user_id,
                #     message=entry["content"],
                #     response="",  # Ignore assistant responses
                # )

        # Process the request with chat history
        response = await self.graph.process(message, user_id, chat_history)

        # Update the original chat history with the new interaction
        # chat_history.append({"role": "user", "content": message})
        # chat_history.append({"role": "assistant", "content": response})

        # Limit chat history size
        if len(chat_history) > self.config.max_chat_history * 2:
            chat_history = chat_history[-self.config.max_chat_history * 2:]

        # Update chat history in self.chat_histories
        self.chat_histories[user_id] = chat_history

        return response
    
    def clear_chat_history(self, user_id: str) -> None:
        """Clear chat history for a specific user."""
        if user_id in self.chat_histories:
            self.chat_histories[user_id] = []

    def get_chat_history(self, user_id: str) -> List[Dict[str, str]]:
        """Get chat history for a specific user."""
        return [
            entry for entry in self.chat_histories.get(user_id, [])
            if entry["role"] == "user"
        ]


if __name__ == "__main__":
    config = ChatbotConfig(
        llm_type={"paid": "openai", "free": "openai"},
        api_base_url="https://api.weekly.vn",
        max_context_length=10,
        timeout=1000,
        human_in_loop_threshold=5,
        max_retries=5,
        rag_docs_path="./internal_docs",
        db_uri="sqlite:///db.sqlite",
        max_chat_history=4,
    )
    chatbot = ChatbotCore(config, domains_dir="./src/domains")


    async def chat_loop():
        user_id = "user123"
        print("Chào bạn! Hãy nhập câu hỏi (gõ 'exit' để thoát):")

        while True:
            question = input("Bạn: ")
            # question = "Trần Tùng Dương đi muộn bao nhiêu lần trong tháng 1,2,3?"
            # question = "Tháng 2 đi muộn bao nhiêu lần?"

            if question.lower() == "exit":
                break

            try:
                response = await chatbot.process_request(question, user_id)
                print(f"Bot: {response}\n")
            except Exception as e:
                print(f"Lỗi: {str(e)}\n")


    import asyncio

    asyncio.run(chat_loop())
