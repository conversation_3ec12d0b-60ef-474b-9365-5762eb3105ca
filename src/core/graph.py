import asyncio
import json
import logging
import os
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Dict, Optional, TypedDict, List, Any

from dotenv import load_dotenv
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel, Field

from src.modules.router.subdomain_router import SubdomainRouter
from src.agents.agent_internal.tardiness_agent.tardiness_agent import TardinessAgent
from src.agents.agent_internal.timekeeping_agent.timekeeping_agent import TimekeepingAgent
from src.utils.utils import validate_llm_response, format_json_response
from langfuse.decorators import langfuse_context, observe

class PayrollAgent:
    def __init__(self, function_caller, llm):
        self.function_caller = function_caller
        self.llm = llm

    @observe(name="pay_roll_agent")
    async def execute(self, state, tools):
        api_data = tools["payroll"][0]  # Giả lập API lương
        params = {"employee_id": state["employee_id"], "time_period": state["entities"]["time_period"]}
        result = await self.function_caller.execute({"path": api_data["path"], "method": "POST", "params": params})
        self.last_api_result = result
        return f"Lương tháng này của bạn: {result['data']['total']}"

class LeaveAgent:
    def __init__(self, function_caller, llm):
        self.function_caller = function_caller
        self.llm = llm

    @observe(name="leave_agent")
    async def execute(self, state, tools):
        api_data = tools["leave"][0]  # Giả lập API nghỉ phép
        params = {"employee_id": state["employee_id"]}
        result = await self.function_caller.execute({"path": api_data["path"], "method": "POST", "params": params})
        self.last_api_result = result
        return f"Số ngày nghỉ còn lại: {result['data']['remaining_days']}"


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()
SYSTEM_PROMPT = os.getenv("SYSTEM_PROMPT")
PLANNER_PROMPT = os.getenv("PLANNER_PROMPT", "You are an intelligent planning agent for an internal chatbot system")
REFLECTION_PROMPT = os.getenv("REFLECTION_PROMPT", "Analyze the execution and result to improve response quality")

class AgentType(str, Enum):
    PLANNER = "planner"
    EXECUTOR = "executor"
    ROUTER = "router"
    TIME_EXTRACTOR = "time_extractor"
    EMPLOYEE_FINDER = "employee_finder"
    REFLECTION = "reflection"
    CASUAL = "casual"
    HUMAN_ASSISTANCE = "human_assistance"

class ExecutionStatus(str, Enum):
    PLANNING = "planning"
    ROUTING = "routing"
    EXECUTING = "executing"
    REFLECTING = "reflecting"
    COMPLETED = "completed"
    ERROR = "error"
    NEEDS_HUMAN_HELP = "needs_human_help"

class Plan(BaseModel):
    steps: List[Dict[str, str]] = Field(default_factory=list)
    current_step: int = 0
    requires_employee_info: bool = False
    requires_time_extraction: bool = False
    requires_external_api: bool = False
    api_endpoints: List[str] = Field(default_factory=list)
    complex_query: bool = False
    concurrent_steps: List[List[Dict[str, str]]] = Field(default_factory=list)

    @observe(name="get_current_step_tool")
    def get_current_step(self) -> Optional[Dict[str, str]]:
        if 0 <= self.current_step < len(self.steps):
            return self.steps[self.current_step]
        return None

    def advance(self) -> None:
        self.current_step += 1

    @observe(name="is_completed_tool")
    def is_completed(self) -> bool:
        return self.current_step >= len(self.steps)

class ChatbotState(TypedDict):
    # User and message information
    user_id: str
    message: str
    domain: str

    # Context and state tracking
    context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    execution_status: ExecutionStatus
    current_agent: AgentType
    previous_agents: List[AgentType]

    # Planning and execution
    plan: Optional[Plan]
    execution_steps: List[Dict[str, Any]]
    execution_results: Dict[str, Any]
    parallel_tasks_results: Dict[str, Any]

    # Routing and domain information
    subdomains: str
    entities: Dict[str, Any]

    # Agent state
    agent: Optional[Any]
    agent_name: str

    # Response and results
    response: Optional[str]
    intermediate_responses: List[str]
    api_result: Optional[Dict[str, Any]]
    employee_id: Optional[str]

    # Error handling
    error_message: Optional[str]
    retry_count: int
    human_feedback: Optional[str]

    # Reflection data
    reflection_notes: List[str]
    confidence_score: float

    # Agent-specific results
    agent_results: Dict[str, Any]

    # Sensitive or confirm infor
    sensitive_action_pending: bool
    user_confirmation: Optional[str]

class ChatbotGraph:
    def __init__(self, config, domain: str, llm, domains_dir: str, function_caller, context_manager):
        self.llm = llm
        self.function_caller = function_caller
        self.context_manager = context_manager
        self.timeout = config.timeout
        self.domain = domain
        self.domains_dir = domains_dir
        self.subdomain_router = SubdomainRouter(self.llm, domains_dir)
        self.memory_save = MemorySaver()

        self.agents = self._load_agents()

        self.subdomains = ["attendance", "payroll", "leave", "performance"]
        self.tools = self.get_list_tools_domain(self.subdomains)
        self.get_employee_id_tool = self._load_employee_tools()

        # Build Graph
        self.graph = self._build_graph()
        self.human_in_loop_threshold = config.human_in_loop_threshold
        self.max_retries = config.max_retries

    def _load_employee_tools(self) -> Dict:
        employee_api_path = os.path.join(self.domains_dir, self.domain, "common", "apis", "employee_list_post.json")
        try:
            with open(employee_api_path, 'r') as file:
                return json.load(file)
        except FileNotFoundError:
            with open(
                '/home/<USER>/Documents/AI/HRBL_AI/chatbot-rbl/src/domains/hr/attendance/apis/employee_list_post.json',
                'r') as file:
                return json.load(file)

    def _load_agents(self) -> Dict[str, Dict[str, object]]:
        agents = {
            "attendance": {
                "tardiness": TardinessAgent(self.function_caller, self.llm),
                "timekeeping": TimekeepingAgent(self.function_caller, self.llm)
            },
            "payroll": {
                "payroll": PayrollAgent(self.function_caller, self.llm)
            },
            "leave": {
                "leave": LeaveAgent(self.function_caller, self.llm)
            }
        }
        return agents

    def _build_graph(self) -> StateGraph:
        graph = StateGraph(ChatbotState)
        graph.add_node("initialize", self._initialize)
        graph.add_node("check_history", self._check_history)
        graph.add_node("handle_casual", self._handle_casual)
        graph.add_node("route_to_subdomain", self._route_to_subdomain)
        graph.add_node("create_plan", self._create_plan)
        graph.add_node("plan_router", self._plan_router)
        graph.add_node("aggregate_results", self._aggregate_results)
        graph.add_node("reflection", self._reflection)
        graph.add_node("human_assistance", self._human_assistance)
        graph.add_node("update_context", self._update_context)
        graph.add_node("generate_response", self._generate_response)
        graph.add_node("fallback", self._fallback)

        # Set entry point
        graph.set_entry_point("initialize")

        # Main flow
        graph.add_conditional_edges(
            "initialize",
            lambda state: "check_history"
        )

        graph.add_conditional_edges(
            "check_history",
            lambda state: "handle_casual" if state.get("current_agent") == AgentType.CASUAL
            else "route_to_subdomain"
        )

        graph.add_edge("handle_casual", "update_context")

        graph.add_conditional_edges(
            "route_to_subdomain",
            lambda state: "create_plan" if state.get("subdomains") else "fallback"
        )

        graph.add_edge("create_plan", "plan_router")

        graph.add_conditional_edges(
            "plan_router",
            lambda state: "aggregate_results" if state.get("execution_status") == ExecutionStatus.REFLECTING
            else "plan_router" if state.get("execution_status") == ExecutionStatus.ROUTING
            else "fallback" if state.get("error_message")
            else "plan_router"
        )

        graph.add_edge("aggregate_results", "reflection")

        graph.add_conditional_edges(
            "reflection",
            lambda state: "generate_response" if state.get("confidence_score", 0) > 0.7
            else "human_assistance" if state.get("confidence_score", 0) < 0.3
            else "plan_router"  # Retry với insights
        )

        graph.add_edge("human_assistance", "generate_response")
        graph.add_edge("generate_response", "update_context")
        graph.add_edge("fallback", "update_context")
        graph.add_edge("update_context", END)

        return graph.compile()

    async def _initialize(self, state: ChatbotState) -> ChatbotState:
        """Initialize the state with default values"""
        state.setdefault("execution_status", ExecutionStatus.PLANNING)
        state.setdefault("current_agent", AgentType.PLANNER)
        state.setdefault("previous_agents", [])
        state.setdefault("plan", None)
        state.setdefault("execution_steps", [])
        state.setdefault("execution_results", {})
        state.setdefault("parallel_tasks_results", {})
        state.setdefault("intermediate_responses", [])
        state.setdefault("retry_count", 0)
        state.setdefault("reflection_notes", [])
        state.setdefault("confidence_score", 1.0)
        state.setdefault("entities", {})
        state.setdefault("agent_name", "")
        state.setdefault("agent_results", {})

        # Setup conversation history
        state.setdefault("conversation_history", [])
        if state["context"].get("short_term_history"):
            state["conversation_history"].extend(state["context"]["short_term_history"])
        if state["context"].get("long_term_history"):
            state["conversation_history"].extend(state["context"]["long_term_history"])

        logger.info(f"Initialized state for user {state['user_id']}")
        return state

    async def _check_history(self, state: ChatbotState) -> ChatbotState:
        """Enhanced history checking with better context retrieval"""
        casual_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "Instructions:\n"
            "1. Determine if the question is a casual greeting (e.g., 'xin chào', 'hello') or unrelated to internal company data (e.g., 'how's the weather?', 'tell me a joke').\n"
            "2. If it is casual or unrelated, respond with exactly 'CASUAL' (nothing else).\n"
            "3. If it relates to internal company data (e.g., tardiness, timesheet, employee info), respond with exactly 'DATA_QUERY' (nothing else).\n"
            "Be very precise in your classification."
        )

        try:
            casual_response = await self.llm.process_message(state["message"], state["context"], casual_prompt)
            if isinstance(casual_response, str) and "CASUAL" in casual_response.strip().upper():
                state["current_agent"] = AgentType.CASUAL
                logger.info(f"Classified as casual interaction for user {state['user_id']}")
                return state
        except Exception as e:
            logger.error(f"Error checking casual intent: {str(e)}")

        existing_entry = self.context_manager.check_existing_question(state["user_id"], state["message"])
        if existing_entry:
            logger.info(f"Found exact match in history for user {state['user_id']}")
            state["response"] = existing_entry["response"]
            state["execution_status"] = ExecutionStatus.COMPLETED
            return state

        similar_entries = self._semantic_search_history(state["user_id"], state["message"])
        if similar_entries:
            most_similar = similar_entries[0]
            if most_similar["similarity"] > 0.92:
                logger.info(f"Found very similar question in history for user {state['user_id']}")
                state["response"] = most_similar["entry"]["response"]
                state["execution_status"] = ExecutionStatus.COMPLETED
                return state
            state["context"]["similar_questions"] = [
                {"question": entry["entry"]["message"],
                 "answer": entry["entry"]["response"],
                 "similarity": entry["similarity"]}
                for entry in similar_entries[:3]
            ]

        return state

    def _semantic_search_history(self, user_id, query):
        results = []
        history = self.context_manager.get_context(user_id).get("short_term_history", []) + \
                 self.context_manager.get_context(user_id).get("long_term_history", [])
        for entry in history:
            if query.lower() in entry["message"].lower() or query.lower() in entry["response"].lower():
                results.append({"entry": entry, "similarity": 0.95})
        return sorted(results, key=lambda x: x["similarity"], reverse=True)

    async def _handle_casual(self, state: ChatbotState) -> ChatbotState:
        """Handle casual interactions with a friendly conversational tone"""
        casual_response_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "This has been classified as a casual interaction. Provide a friendly, conversational response.\n"
            "If the user greeted in Vietnamese, respond in Vietnamese. Otherwise respond in English.\n"
            "Keep it brief, friendly and natural."
        )

        try:
            response = await self.llm.process_message(state["message"], state["context"], casual_response_prompt)
            state["response"] = response.strip() if isinstance(response,
                                                               str) else "I'm here to help with HR questions."
            state["execution_status"] = ExecutionStatus.COMPLETED
            logger.info(f"Handled casual interaction for user {state['user_id']}")
        except Exception as e:
            logger.error(f"Error handling casual interaction: {str(e)}")
            state["response"] = "I'm here to help with HR questions. How can I assist you today?"
            state["execution_status"] = ExecutionStatus.COMPLETED

        return state

    async def _route_to_subdomain(self, state: ChatbotState) -> ChatbotState:
        try:
            state = await asyncio.wait_for(
                self.subdomain_router.route_to_subdomains(state),
                timeout=self.timeout
            )
            if not state.get("subdomains"):
                state["error_message"] = "I couldn't understand which department can help."
        except asyncio.TimeoutError:
            state["error_message"] = "Request took too long. Please try a more specific question."
        except Exception as e:
            logger.error(f"Error routing to subdomain: {str(e)}")
            state["error_message"] = "Issue understanding your request."
        return state

    async def _create_plan(self, state: ChatbotState) -> ChatbotState:
        if not state["subdomains"]:
            logger.warning(f"No subdomains identified for user {state['user_id']}, using default plan")
            state["response"] = "I'm here to help with HR questions."
            return state

        agent_info = []
        for subdomain in state["subdomains"]:
            if subdomain in self.agents:
                for agent_name, agent in self.agents[subdomain].items():
                    agent_info.append(
                        f"- Name: {agent_name}, "
                        f"Subdomain: {subdomain}, "
                        f"Description: {agent.__class__.__name__} handles {subdomain} queries"
                    )
        agent_info_str = "\n".join(agent_info)

        plan_prompt = (
            f"{PLANNER_PROMPT}\n"
            f"Query: '{state['message']}'\n"
            f"Subdomains: {', '.join(state['subdomains'])}\n"
            f"Available Agents:\n{agent_info_str}\n"
            "Instructions:\n"
            "1. Analyze the query thoroughly:\n"
            "   - Identify any employee name (e.g., proper nouns like 'Phong', 'Ngô Gia Phong'). If present, set 'requires_employee_info' to true.\n"
            "   - Detect any time period, explicit or implicit:\n"
            "     - Implicit: Vague or relative terms (e.g., 'this month', 'last week', 'today') → set 'requires_time_extraction' to true.\n"
            "     - If no time period is mentioned, assume the current month and set 'requires_time_extraction' to true.\n"
            "   - Match the query intent to a subdomain and select the most relevant agent based on context and available agents.\n"
            "2. Build a plan as a list of step objects:\n"
            "   - If an employee name is detected, include {{'action': 'get_employee_id'}}.\n"
            "   - If time extraction is needed (vague or implicit time), include {{'action': 'extract_time'}}.\n"
            "   - For each relevant agent, include {{'action': 'call_agent', 'agent_name': '<agent_name>'}}.\n"
            "3. Return a JSON object:\n"
            "   {{'steps': [], 'requires_employee_info': bool, 'requires_time_extraction': bool, "
            "'requires_external_api': bool, 'api_endpoints': [], 'complex_query': bool, 'concurrent_steps': [[]]}}"
        )

        try:
            response = await self.llm.process_message(state["message"], state["context"], plan_prompt)
            cleaned_response = response.strip()
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()

            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"

            plan_dict = json.loads(cleaned_response)
            state["plan"] = Plan(**plan_dict)
            state["execution_status"] = ExecutionStatus.ROUTING

            logger.info(f"Created plan for user {state['user_id']}")

            state["execution_steps"].append({
                "step": "create_plan",
                "timestamp": datetime.now().isoformat(),
                "result": "Plan created successfully"
            })
        except Exception as e:
            logger.error(f"Error creating plan: {str(e)}")
            state["plan"] = Plan(
                steps=[{"action": "get_employee_id"}, {"action": "extract_time"},
                       {"action": "call_agent", "agent_name": "tardiness"},
                       {"action": "call_agent", "agent_name": "timekeeping"}],
                requires_employee_info=True,
                requires_time_extraction=True
            )
            state["execution_steps"].append({
                "step": "create_plan",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error creating plan, using default: {str(e)}"
            })

        return state

    async def _plan_router(self, state: ChatbotState) -> ChatbotState:
        if not state["plan"]:
            state["error_message"] = "No plan available."
            state["execution_status"] = ExecutionStatus.ERROR
            return state

        current_step = state["plan"].get_current_step()
        if not current_step:
            if state["plan"].is_completed():
                state["execution_status"] = ExecutionStatus.REFLECTING
                logger.info(f"Plan completed for user {state['user_id']}, moving to aggregation")
            else:
                state["error_message"] = "No valid steps in plan."
                state["execution_status"] = ExecutionStatus.ERROR
            return state

        action = current_step.get("action")
        logger.info(f"Processing step: {action} for user {state['user_id']}")

        if action == "get_employee_id":
            state = await self._get_employee_id(state)
        elif action == "extract_time":
            if not state["entities"].get("time_period"):
                state = await self._extract_time(state)
        elif action == "call_agent":
            agent_name = current_step.get("agent_name")
            intent = current_step.get("intent", "check")
            subdomain = next((s for s in self.agents if agent_name in self.agents[s]), None)
            if subdomain and agent_name in self.agents[subdomain]:
                state["agent"] = self.agents[subdomain][agent_name]
                state["agent_name"] = agent_name
                state["execution_status"] = ExecutionStatus.EXECUTING
                logger.info(f"Assigned agent: {agent_name} in subdomain {subdomain}")
                state = await self._execute_agent(state)
                if intent in ["update", "report"]:
                    state["sensitive_action_pending"] = True
            else:
                state["error_message"] = f"Agent {agent_name} not found."
                state["execution_status"] = ExecutionStatus.ERROR
                logger.error(f"Agent not found: {agent_name}")
                return state

        state["plan"].advance()

        if not state["plan"].is_completed():
            state["execution_status"] = ExecutionStatus.ROUTING
        elif state["plan"].is_completed() and not state.get("error_message"):
            state["execution_status"] = ExecutionStatus.REFLECTING

        logger.info(
            f"Plan routing step completed, current_step: {state['plan'].current_step}, status: {state['execution_status']}")
        return state

    # @observe()
    async def _get_employee_id(self, state: ChatbotState) -> ChatbotState:
        prompt = (
            "Extract the employee name from the user query (if present) and use the provided tool to get their employee_id. "
            "If no employee name is found, do not call any tool.\n\n"
            "Instructions:\n"
            "1. Analyze the query for employee name (e.g., 'Phong')\n"
            "2. If found, call 'employee_list_post' with 'search' parameter\n"
            "3. If no name, skip tool call"
        )
        try:
            response = await self.llm.process_message_with_tools(
                message=state["message"],
                context=state["context"],
                system_prompt=prompt,
                tools=[self.get_employee_id_tool]
            )
            if not response.tool_calls:
                logger.info("No employee name detected in query")
                return state

            if response.tool_calls and response.tool_calls[0].function.name == "employee_list_post":
                toolcall_arguments = response.tool_calls[0].function.arguments
                if not validate_llm_response(toolcall_arguments):
                    toolcall_arguments = format_json_response(toolcall_arguments)
                params = json.loads(toolcall_arguments)
                if not params.get("search"):
                    state["error_message"] = "No matching employee found. Please provide a valid employee name."
                    return state

                function_data = {
                    "path": self.get_employee_id_tool['function']['path'],
                    "method": self.get_employee_id_tool['function']['method'],
                    "params": params
                }
                result = await self.function_caller.execute(function_data)

                if not result.get('data') or len(result.get('data')) == 0:
                    state["error_message"] = "No matching employee found. Please provide a valid employee name."
                    return state

                if len(result['data']) > 1:
                    employee_list = "\n".join([f"- {emp.get('name', 'Unknown')}" for emp in result['data'][:5]])
                    state["error_message"] = (
                        f"I found multiple employees:\n{employee_list}\n\n"
                        f"Please provide the full name."
                    )
                    return state

                state["employee_id"] = result['data'][0]['id']

        except Exception as e:
            logger.error(f"Error in employee identification: {str(e)}")
            state["error_message"] = "Unable to process your request. Please try again."
        return state

    # @observe()
    async def _extract_time(self, state: ChatbotState) -> ChatbotState:
        current_date = datetime.now().strftime("%Y-%m-%d")
        prompt = (
            f"Current date: {current_date}\n"
            f"Query: '{state['message']}'\n"
            "Extract the time period mentioned in the query and return the start and end dates in 'YYYY-MM-DD' format.\n"
            "Rules:\n"
            "- If the query refers to a single day (e.g., 'yesterday'), use the same date for both start and end.\n"
            "- If it refers to a month (e.g., 'March 2025'), use the first and last days of that month.\n"
            "- If it refers to 'last week', return the Monday to Sunday of the previous week.\n"
            "- If no time period is mentioned, default to the current month.\n"
            "Use the current date ({current_date}) as the reference for all calculations.\n"
            "Return ONLY valid JSON in this format:\n"
            '{"start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD"}'
        )

        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            cleaned = response.strip().replace("```json", "").replace("```", "").strip()
            if cleaned.startswith("{{") and cleaned.endswith("}}"):
                cleaned = cleaned[1:-1]
            elif cleaned.startswith("{") and cleaned.endswith("}"):
                pass
            else:
                cleaned = f"{{{cleaned}}}"

            time_period = json.loads(cleaned.strip())
            state["entities"]["time_period"] = time_period
        except Exception as e:
            logger.error(f"Error extracting time: {str(e)}")
            today = datetime.now()
            start_date = today.replace(day=1).strftime("%Y-%m-%d")
            end_date = (today.replace(day=1) + timedelta(days=31)).replace(day=1) - timedelta(days=1)
            state["entities"]["time_period"] = {"start_date": start_date, "end_date": end_date.strftime("%Y-%m-%d")}
            state["error_message"] = "Couldn’t parse the time period clearly. Defaulting to this month."

        return state

    async def _execute_agent(self, state: ChatbotState) -> ChatbotState:
        agent = state["agent"]
        agent_name = state["agent_name"]

        try:
            state.setdefault("agent_results", {})
            result = await asyncio.wait_for(
                agent.execute(state, self.tools),
                timeout=self.timeout
            )

            state["agent_results"][agent_name] = {
                "response": result,
                "api_result": agent.last_api_result if hasattr(agent, "last_api_result") else None
            }

            state["execution_steps"].append({
                "step": "execute_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Agent {agent_name} executed successfully"
            })

        except Exception as e:
            logger.error(f"Agent execution failed: {str(e)}")
            state["agent_results"][agent_name] = {
                "response": f"Error: {str(e)}",
                "api_result": None
            }
            state["retry_count"] = state.get("retry_count", 0) + 1

            state["execution_steps"].append({
                "step": "execute_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error executing {agent_name}: {str(e)}"
            })

        return state

    async def _aggregate_results(self, state: ChatbotState) -> ChatbotState:
        """Aggregate results from all executed agents"""
        try:
            state.setdefault("agent_results", {})
            logger.info(f"Aggregating results for user {state['user_id']}: {state['agent_results']}")

            if not state["agent_results"]:
                state["error_message"] = "No results from agents to aggregate."
                state["execution_status"] = ExecutionStatus.ERROR
                return state

            aggregated_response = ""
            for agent_name, result in state["agent_results"].items():
                if "Error" not in result["response"]:
                    aggregated_response += f"{result['response']}\n"

            if not aggregated_response:
                aggregated_response = "Xin lỗi, mình không có đủ thông tin để trả lời đầy đủ câu hỏi của bạn."

            state["response"] = aggregated_response.strip()
            state["execution_results"]["aggregated"] = state["agent_results"]
            state["execution_status"] = ExecutionStatus.REFLECTING

            state["execution_steps"].append({
                "step": "aggregate_results",
                "timestamp": datetime.now().isoformat(),
                "result": "Results aggregated successfully"
            })

        except Exception as e:
            logger.error(f"Error aggregating results: {str(e)}")
            state["error_message"] = "Could not aggregate results from agents."
            state["execution_status"] = ExecutionStatus.ERROR
            state["execution_steps"].append({
                "step": "aggregate_results",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })

        return state

    async def _reflection(self, state: ChatbotState) -> ChatbotState:
        """Reflect on the execution and improve response quality"""
        try:
            reflection_prompt = (
                f"{REFLECTION_PROMPT}\n\n"
                f"Execution results: {json.dumps(state['execution_results'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Analyze the execution results and identify any issues or areas for improvement.\n"
                "2. Provide a confidence score between 0 and 1 for the overall response quality.\n"
                "3. If the confidence score is below 0.7, suggest improvements or additional steps.\n"
                "4. Return your response as a valid JSON object with the following structure:\n"
                "{\n"
                "  \"confidence_score\": float,\n"
                "  \"reflection_notes\": [\"note1\", \"note2\", ...],\n"
                "  \"suggested_steps\": [\"step1\", \"step2\", ...]\n"
                "}"
            )

            response = await self.llm.process_message(state["message"], state["context"], reflection_prompt)
            cleaned_response = response.strip()
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()

            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"

            reflection_dict = json.loads(cleaned_response)
            state["confidence_score"] = reflection_dict.get("confidence_score", 1.0)
            state["reflection_notes"] = reflection_dict.get("reflection_notes", [])
            if state["plan"] and reflection_dict.get("suggested_steps"):
                state["plan"].steps.extend(reflection_dict.get("suggested_steps", []))

            state["execution_steps"].append({
                "step": "reflection",
                "timestamp": datetime.now().isoformat(),
                "result": f"Reflection completed with confidence score: {state['confidence_score']}"
            })

        except Exception as e:
            logger.error(f"Error during reflection: {str(e)}")
            state["error_message"] = "Could not complete the reflection process. Please try again."
            state["execution_steps"].append({
                "step": "reflection",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })

        return state

    async def _human_assistance(self, state: ChatbotState) -> ChatbotState:
        """Request human assistance for complex queries or low confidence responses"""
        try:
            human_assistance_prompt = (
                "The chatbot has encountered a complex query or low confidence response.\n\n"
                f"User query: '{state['message']}'\n\n"
                "Execution results:\n"
                f"{json.dumps(state['execution_results'], indent=2)}\n\n"
                "Reflection notes:\n"
                f"{json.dumps(state['reflection_notes'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Review the execution results and reflection notes.\n"
                "2. Provide a response to the user or suggest additional steps for the chatbot.\n"
                "3. Return your response as a valid JSON object with the following structure:\n"
                "{\n"
                "  \"response\": \"string\",\n"
                "  \"suggested_steps\": [\"step1\", \"step2\", ...]\n"
                "}"
            )

            response = await self.llm.process_message(state["message"], state["context"], human_assistance_prompt)
            cleaned_response = response.strip()
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()

            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"

            human_assistance_dict = json.loads(cleaned_response)
            state["response"] = human_assistance_dict.get("response",
                                                          "I'm here to help with HR questions. How can I assist you today?")
            if state["plan"] and human_assistance_dict.get("suggested_steps"):
                state["plan"].steps.extend(human_assistance_dict.get("suggested_steps", []))

            state["execution_steps"].append({
                "step": "human_assistance",
                "timestamp": datetime.now().isoformat(),
                "result": "Human assistance provided"
            })

        except Exception as e:
            logger.error(f"Error during human assistance: {str(e)}")
            state["error_message"] = "Could not complete the human assistance process. Please try again."
            state["execution_steps"].append({
                "step": "human_assistance",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })

        return state

    async def _update_context(self, state: ChatbotState) -> ChatbotState:
        """Update the context with the latest state information"""
        try:
            response = state.get("response", "No response generated yet.")
            self.context_manager.update_context(
                state["user_id"],
                state["message"],
                response,
                state.get("api_result")
            )
            state["execution_steps"].append({
                "step": "update_context",
                "timestamp": datetime.now().isoformat(),
                "result": "Context updated successfully"
            })
        except Exception as e:
            logger.error(f"Error updating context: {str(e)}")
            state["error_message"] = "Could not update the context. Please try again."
            state["execution_steps"].append({
                "step": "update_context",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })

        return state

    async def _generate_response(self, state: ChatbotState) -> ChatbotState:
        """Generate the final response for the user"""
        try:
            response_prompt = (
                "Generate a final response for the user based on the execution results and context.\n\n"
                f"User query: '{state['message']}'\n\n"
                "Execution results:\n"
                f"{json.dumps(state['execution_results'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Review the execution results and context.\n"
                "2. Generate a final response for the user.\n"
                "3. Return your response as a string."
            )

            response = await self.llm.process_message(state["message"], state["context"], response_prompt)
            state["response"] = response.strip() if isinstance(response,
                                                               str) else "I'm here to help with HR questions. How can I assist you today?"
            state["execution_status"] = ExecutionStatus.COMPLETED

            state["execution_steps"].append({
                "step": "generate_response",
                "timestamp": datetime.now().isoformat(),
                "result": "Response generated successfully"
            })

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            state["error_message"] = "Could not generate the response. Please try again."
            state["execution_steps"].append({
                "step": "generate_response",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })

        return state

    async def _fallback(self, state: ChatbotState) -> ChatbotState:
        prompt = (
            f"{SYSTEM_PROMPT}"
            f"Query: {state['message']}\n"
            f"Couldn't process due to: {state['error_message']}\n"
            "Ask for more details in Vietnamese if query is Vietnamese, else English."
        )
        state["response"] = await self.llm.process_message(state["message"], state["context"], prompt)
        return state

    def get_list_tools_domain(self, subdomains: list) -> Dict:
        tools = {}
        for subdomain in subdomains:
            tools[subdomain] = []
            apis_subdomain_dir = os.path.join(self.domains_dir, self.domain, subdomain, "apis")
            if os.path.isdir(apis_subdomain_dir):
                for api_domain in os.listdir(apis_subdomain_dir):
                    with open(os.path.join(apis_subdomain_dir, api_domain), "r") as f:
                        data_api = json.load(f)
                    tools[subdomain].append(data_api)
        return tools

    async def process(self, message: str, user_id: str) -> str:
        initial_state = {
            "domain": "hr",
            "message": message,
            "user_id": user_id,
            "context": self.context_manager.get_context(user_id),
            "subdomains": "",
            "entities": {},
            "agent": None,
            "agent_name": None,
            "response": None,
            "api_result": None,
            "employee_id": None,
            "error_message": None
        }

        result = await self.graph.ainvoke(initial_state)
        return result["response"]

