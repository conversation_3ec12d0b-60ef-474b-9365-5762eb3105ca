import asyncio
import json
import logging
import os
import re
import sqlite3

import pytz
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set, Tuple

from dateutil.relativedelta import relativedelta
from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

from src.modules.router.subdomain_router import SubdomainRouter
from src.agents.agent_internal.timesheet_agent.timesheet_agent import TimesheetAgent
from src.utils.utils import format_json_response

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
SYSTEM_PROMPT = os.getenv("SYSTEM_PROMPT")
DB_FILE="../database/hr_management.db"

class Plan(BaseModel):
    steps: List[Dict[str, Any]] = Field(default_factory=list)
    contingencies: Dict[str, List[Dict[str, Any]]] = Field(default_factory=dict)
    current_step: int = 0

    def get_current_step(self) -> Optional[Dict[str, str]]:
        return self.steps[self.current_step] if 0 <= self.current_step < len(self.steps) else None

    def get_contingency(self, step_id: str) -> Optional[List[Dict[str, Any]]]:
        return self.contingencies.get(step_id, [])

    def advance(self) -> None:
        self.current_step += 1

    def is_completed(self) -> bool:
        return self.current_step >= len(self.steps)


class DatabaseHandler:
    @staticmethod
    def connect_db():
        return sqlite3.connect(DB_FILE)

    @staticmethod
    def execute_query(query: str, params: List = None) -> List[Dict]:
        conn = DatabaseHandler.connect_db()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"Database query error: {e}")
            raise
        finally:
            conn.close()


class StatisticsProcessor:

    STATISTICS_METRICS = {
        "count": {
            "function": lambda data: data[0]["count"] if data else 0,
            "response_template": "Có {result} nhân viên trong {scope_description}."
        },
        "list_names": {
            "function": lambda data: [emp["name"] for emp in data],
            "response_template": "Danh sách nhân viên trong {scope_description}: {result}."
        },
        "list_details": {
            "function": lambda data: [{"name": emp["name"], "email": emp["email"], "position": emp["position"],
                                     "department": emp["department"], "employment_type": emp["employment_type"],
                                     "years_worked": emp["years_worked"]} for emp in data],
            "response_template": "Thông tin nhân viên trong {scope_description}:\n{result}"
        },
        "average_years": {
            "function": lambda data: data[0]["avg_years"] if data else 0,
            "response_template": "Trung bình năm làm việc của nhân viên trong {scope_description} là {result:.1f} năm."
        },
        "full_time_count": {
            "function": lambda data: data[0]["count"] if data else 0,
            "response_template": "Số nhân viên full-time trong {scope_description}: {result} người."
        },
        "department_list": {
            "function": lambda data: [emp["department"] for emp in data],
            "response_template": "Các bộ phận trong công ty: {result}."
        },
        "employee_ids": {
            "function": lambda data: [emp["id"] for emp in data],
            "response_template": "Found {result} employee IDs in {scope_description}."
        }
    }

    def __init__(self, llm):
        self.llm = llm
        self.db_handler = DatabaseHandler()

    def process_statistics(self, data, metric, scope):
        if metric not in self.STATISTICS_METRICS:
            return {"response": "Loại thống kê không được hỗ trợ.", "data": {}}

        metric_config = self.STATISTICS_METRICS[metric]
        try:
            result = metric_config["function"](data)
            scope_description = self._get_scope_description(scope)

            if not result and metric not in ["count", "full_time_count", "average_years"]:
                return {"response": f"Không tìm thấy dữ liệu trong {scope_description}.", "data": result}

            formatted_result = self._format_result(result, metric)
            response = metric_config["response_template"].format(result=formatted_result,
                                                               scope_description=scope_description)
            return {"response": response, "data": result}
        except Exception as e:
            logger.error(f"Error processing statistic {metric}: {e}")
            return {"response": "Có lỗi khi xử lý thống kê.", "data": {}}

    def _get_scope_description(self, scope):
        scope_type = scope.get("type")
        scope_value = scope.get("value", "")
        if scope_type == "individual":
            return f"nhân viên {scope_value}"
        elif scope_type == "department":
            return f"bộ phận {scope_value}"
        elif scope_type == "position":
            return f"chức vụ {scope_value}"
        elif scope_type == "company":
            return "toàn công ty"
        return "toàn công ty"

    def _format_result(self, result, metric):
        if metric in ["count", "full_time_count"]:
            return str(result)
        elif metric == "list_names":
            return ", ".join(result) if result else "không có nhân viên nào"
        elif metric == "list_details":
            lines = [f"- {emp['name']} ({emp['email']}, {emp['position']}, {emp['department']})" for emp in result]
            return "\n".join(lines) if lines else "không có nhân viên nào"
        elif metric == "average_years":
            return f"{result:.1f}" if result else "0"
        elif metric == "department_list":
            return ", ".join(result) if result else "không có bộ phận nào"
        elif metric == "employee_ids":
            return str(len(result)) if result else "0"
        return str(result)


class ChatbotState(dict):
    user_id: str
    domain: str
    message: str
    context: Dict[str, Any]
    plan: Optional[Plan]
    subdomains: List[str]
    entities: Dict[str, Any]
    agent_results: Dict[str, Dict[str, Any]]
    response: Optional[str]
    error_message: Optional[str]
    retry_count: Dict[str, int]
    employee_id: Optional[str]
    attempts: int
    dependencies: Dict[str, Any]
    chat_history: List[Dict[str, str]]

class QuestionAnalyzer:
    def __init__(self, llm, sub_agents):
        self.llm = llm
        self.sub_agents = sub_agents

    async def analyze_question(self, state: dict) -> Dict[str, Any]:
        agent_info = []
        for subdomain, agents in self.sub_agents.items():
            for agent_name, agent_data in agents.items():
                agent_info.append({
                    "agent_name": agent_name,
                    "subdomain": subdomain,
                    "description": agent_data.get("description", "No description provided")
                })
        prompt = f"""
        Analyze the question: '{state["message"]}'

        Instructions:
        1. Identify the intent:
           - "statistics": Queries about employee counts, lists, or averages (e.g., count, list_details, full_time_count, department_list).
           - "timesheet": Queries about attendance data (e.g., late check-ins, early check-outs, absences, leave days).
           - "payroll": Queries about salary or compensation.
           - "leave": Queries about leave balances or requests.
           - "performance": Queries about employee performance metrics.
           - "casual": Non-data-related questions (e.g., greetings, general chat).
        2. Identify the scope:
           - "individual": Specific employee (identified by name or email).
           - "department": Specific department (e.g., "Sales").
           - "team": Specific team (treated as a department subset).
           - "position": Specific job role (e.g., "Engineer").
           - "company": All employees.
        3. Extract entities:
           - metric: For statistics (e.g., "count", "list_names", "average_years").
           - time_period: For timesheet/payroll/leave (e.g., "this month", "April 2025").
           - department: Department name (e.g., "Sales").
           - team: Team name (e.g., "Team A").
           - employee_name: Employee name (e.g., "Nguyen Van A").
           - email: Employee email (e.g., "<EMAIL>").
           - position: Job role (e.g., "Engineer").
        4. Return JSON:
           - intent: str
           - scope: {{ "type": str, "value": str }}
           - entities: {{ key: value }}

        Examples:
        Question: "Công ty có bao nhiêu người?"
        Output: {{
            "intent": "statistics",
            "scope": {{ "type": "company", "value": "" }},
            "entities": {{ "metric": "count" }}
        }}

        Question: "Bộ phận Sales có ai đi muộn tháng này?"
        Output: {{
            "intent": "timesheet",
            "scope": {{ "type": "department", "value": "Sales" }},
            "entities": {{ "time_period": "tháng này" }}
        }}

        Question: "Nguyen Van A có bao nhiêu ngày phép?"
        Output: {{
            "intent": "leave",
            "scope": {{ "type": "individual", "value": "Nguyen Van A" }},
            "entities": {{ "employee_name": "Nguyen Van A" }}
        }}
        """
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            cleaned_response = response.strip().replace("```json", "").replace("```", "")
            analysis = json.loads(cleaned_response)
            return analysis
        except Exception as e:
            logger.error(f"Question analysis failed: {e}")
            return {
                "intent": "casual",
                "scope": {"type": "company", "value": ""},
                "entities": {"error": "Failed to analyze question"}
            }

class PlanCreator:
    def __init__(self, llm, sub_agents):
        self.llm = llm
        self.sub_agents = sub_agents
        self.analyzer = QuestionAnalyzer(llm, sub_agents)

    async def create_plan(self, state: Dict) -> Plan:
        """
        Creates an execution plan using a prompt-driven approach.
        Selects agents based on QuestionAnalyzer's agent_names output.
        """
        analysis = await self.analyzer.analyze_question(state)
        intent = analysis["intent"]
        scope = analysis["scope"]
        entities = analysis["entities"]
        agent_names = analysis.get("agent_names", [])
        contingency_prompt = analysis.get("contingency_prompt", None)

        schema = """
        Table: employees
        Columns:
        - employee_id: INTEGER PRIMARY KEY
        - name: TEXT NOT NULL
        - email: TEXT NOT NULL UNIQUE
        - hr_code: TEXT NOT NULL
        - start_date: TEXT NOT NULL
        - position: TEXT NOT NULL
        - department: TEXT NOT NULL
        - status: TEXT NOT NULL DEFAULT 'working'
        - employment_type: TEXT NOT NULL
        - years_worked: INTEGER NOT NULL DEFAULT 0
        """

        agent_info = []
        for subdomain, agents in self.sub_agents.items():
            for agent_name, agent_data in agents.items():
                agent_info.append({
                    "agent_name": agent_name,
                    "subdomain": subdomain,
                    "description": agent_data.get("description", "No description provided")
                })

        prompt = f"""
        Create an execution plan based on the analyzed question.

        Question: '{state['message']}'
        Analysis:
        - Intent: {intent}
        - Scope: {json.dumps(scope, ensure_ascii=False)}
        - Entities: {json.dumps(entities, ensure_ascii=False)}
        - Agent Names: {json.dumps(agent_names, ensure_ascii=False)}
        - Contingency Prompt: {contingency_prompt or 'null'}

        Database Schema:
        {schema}

        Available Agents:
        {json.dumps(agent_info, indent=2, ensure_ascii=False)}

        Instructions:
        1. Generate a plan with minimal steps for three workflows:
           - "statistics" intent: Direct database query for employee data.
           - Agent-based intents (timesheet, payroll, leave, performance): Query employee_ids, extract time (if needed), and call agent(s) from agent_names.
           - "casual" intent: Handle casual conversation without data queries.
        2. Restrict query_db to employee data:
           - Only query the employees table for metrics:
             - "employee_ids": List employee_id.
             - "count": Count employees.
             - "list_details": Return name, email, position, department, employment_type, years_worked.
             - "full_time_count": Count employees with employment_type = 'Fulltime chính thức'.
             - "department_list": List distinct departments.
           - Do NOT query for non-employee data (e.g., late arrivals, payroll, leave balances).
        3. Delegate non-employee data to agents:
           - Use agent_names to select the agent(s) for call_agent steps.
           - If agent_names is empty for non-statistics/non-casual intents, generate a single step to ask for clarification using contingency_prompt (or a default prompt).
        4. Include time extraction for intents requiring time_period.
        5. Define contingencies for each step to handle failures.
        6. Ensure dependencies are correct (e.g., call_agent depends on query_db and extract_time).
        7. Output JSON:
           - steps: [{{
               "id": str,
               "action": str,
               "attributes": dict,
               "depends_on": [str]
             }}]
           - contingencies: {{ "step_id": [{{
               "action": "ask_user",
               "prompt": str
             }}] }}

        Examples:
        Analysis: {{
            "intent": "statistics",
            "scope": {{ "type": "company", "value": "" }},
            "entities": {{ "metric": "count" }},
            "agent_names": [],
            "contingency_prompt": null
        }}
        Output: {{
            "steps": [{{
                "id": "query1",
                "action": "query_db",
                "attributes": {{ "metric": "count", "scope": {{ "type": "company", "value": "" }} }},
                "depends_on": []
            }}],
            "contingencies": {{
                "query1": [{{
                    "action": "ask_user",
                    "prompt": "Không tìm thấy dữ liệu. Vui lòng kiểm tra lại."
                }}]
            }}
        }}

        Analysis: {{
            "intent": "timesheet",
            "scope": {{ "type": "department", "value": "Phòng nhân sự" }},
            "entities": {{ "time_period": "tháng này" }},
            "agent_names": ["timesheet"],
            "contingency_prompt": null
        }}
        Output: {{
            "steps": [
                {{
                    "id": "query1",
                    "action": "query_db",
                    "attributes": {{ "metric": "employee_ids", "scope": {{ "type": "department", "value": "Phòng nhân sự" }} }},
                    "depends_on": []
                }},
                {{
                    "id": "time1",
                    "action": "extract_time",
                    "attributes": {{ "period": "tháng này" }},
                    "depends_on": []
                }},
                {{
                    "id": "agent1",
                    "action": "call_agent",
                    "attributes": {{ "agent_name": "timesheet" }},
                    "depends_on": ["query1", "time1"]
                }}
            ],
            "contingencies": {{
                "query1": [{{
                    "action": "ask_user",
                    "prompt": "Không tìm thấy nhân viên trong Phòng nhân sự. Vui lòng kiểm tra lại."
                }}],
                "time1": [{{
                    "action": "ask_user",
                    "prompt": "Thời gian không rõ. Vui lòng cung cấp thời gian cụ thể (ví dụ: tháng 4/2025)."
                }}],
                "agent1": [{{
                    "action": "ask_user",
                    "prompt": "Không thể xử lý yêu cầu về timesheet. Vui lòng cung cấp thêm chi tiết."
                }}]
            }}
        }}

        Analysis: {{
            "intent": "performance",
            "scope": {{ "type": "company", "value": "" }},
            "entities": {{ "time_period": "tháng này" }},
            "agent_names": [],
            "contingency_prompt": "Không có thông tin về doanh thu. Vui lòng cung cấp câu hỏi liên quan đến nhân viên hoặc công việc."
        }}
        Output: {{
            "steps": [{{
                "id": "clarify1",
                "action": "ask_user",
                "attributes": {{ "prompt": "Không có thông tin về doanh thu. Vui lòng cung cấp câu hỏi liên quan đến nhân viên hoặc công việc." }},
                "depends_on": []
            }}],
            "contingencies": {{
                "clarify1": [{{
                    "action": "ask_user",
                    "prompt": "Câu hỏi vẫn chưa rõ. Vui lòng cung cấp thêm chi tiết."
                }}]
            }}
        }}
        """
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            cleaned_response = response.strip().replace("```json", "").replace("```", "")
            plan_dict = json.loads(cleaned_response)
            return Plan(
                steps=plan_dict["steps"],
                contingencies=plan_dict["contingencies"]
            )
        except Exception as e:
            logger.error(f"Plan creation failed: {e}")
            return Plan(
                contingencies={
                    "initial": [{"action": "ask_user", "prompt": "Câu hỏi chưa rõ. Vui lòng cung cấp thêm chi tiết."}]
                }
            )


    async def analyze_step_result(self, state: ChatbotState, step_id: str) -> Dict[str, Any]:
        if step_id not in state["agent_results"]:
            return {"is_valid": False, "required_data": {}, "adjustments": []}
        prompt = f"""
        Analyze step result for question: '{state['message']}'
        Step ID: {step_id}
        Result: {json.dumps(state['agent_results'].get(step_id, {}), indent=2)}
        Dependencies: {json.dumps(state['dependencies'], indent=2)}

        Instructions:
        1. Check if result is valid:
           - For "query_db": Non-empty data.
           - For "get_employee_ids": Non-empty employee_ids list.
           - For "extract_time": Valid time_period with start_date, end_date.
           - For "call_agent": Status is "success" and data present.
        2. Specify data needed for next steps.
        3. Suggest adjustments if invalid.

        Return JSON:
        {{"is_valid": bool, "required_data": dict, "adjustments": [{{"id": str, "action": str, "attributes": dict, "depends_on": [str]}}]}}
        """
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            analysis = json.loads(response.strip().replace("```json", "").replace("```", ""))
            if analysis["is_valid"]:
                state["dependencies"].setdefault("agent_results", {})
                state["dependencies"]["agent_results"][step_id] = state["agent_results"][step_id]
            return analysis
        except Exception as e:
            logger.error(f"Analysis failed for step {step_id}: {e}")
            return {"is_valid": False, "required_data": {}, "adjustments": []}

class CoordinatorAgent:
    def __init__(self, config, domain: str, llm, domains_dir: str, function_caller, context_manager):
        self.llm = llm
        self.function_caller = function_caller
        self.context_manager = context_manager
        self.timeout = config.timeout
        self.domain = domain
        self.domains_dir = domains_dir
        self.subdomain_router = SubdomainRouter(self.llm, domains_dir)
        self.sub_agents = self._load_agents()
        self.assistant = PlanCreator(self.llm, self.sub_agents)
        self.subdomains = ["attendance", "payroll", "leave", "performance"]
        self.tools = self.get_list_tools_domain(self.subdomains)
        self.max_retries = config.max_retries
        self.db_handler = DatabaseHandler()
        self.stats_processor = StatisticsProcessor(self.llm)
        self.graph = self._build_graph()

    def _build_graph(self) -> CompiledStateGraph:
        graph = StateGraph(ChatbotState)
        graph.add_node("process", self._process)
        graph.set_entry_point("process")
        graph.add_edge("process", END)
        return graph.compile()

    def _load_agents(self) -> Dict[str, Dict[str, object]]:
        agents = {
            "attendance": {
                "timesheet": {
                    "instance": TimesheetAgent(self.function_caller, self.llm),
                    "description": "Handles queries about employee attendance, including late check-ins, early check-outs, absences, and leave days."
                }
            }
        }
        return agents

    async def _process(self, state):
        state.setdefault("retry_count", {})
        state.setdefault("agent_results", {})
        state.setdefault("entities", {})
        state.setdefault("context", self.context_manager.get_context(state["user_id"]))
        state.setdefault("attempts", 0)
        state.setdefault("dependencies", {})
        state.setdefault("conversation_history", [])
        state.setdefault("employee_ids", [])

        if "chat_history" in state and state["chat_history"]:
            state["context"]["history"] = self._extract_chat_history_context(state["chat_history"])["history"]

        if await self._is_casual(state):
            state["response"] = await self._handle_casual(state)
            state["conversation_history"].append({"user": state["message"], "response": state["response"]})
            return state

        if not state.get("subdomains"):
            state = await self.subdomain_router.route_to_subdomains(state)
            if not state["subdomains"]:
                state["response"] = "Tôi không hiểu câu hỏi. Vui lòng cung cấp thông tin chi tiết hơn"
                return state

        while state["attempts"] < self.max_retries:
            if not state.get("plan"):
                state["plan"] = await self.assistant.create_plan(state)
            await self._execute_plan(state)
            if state.get("response"):
                state["conversation_history"].append({"user": state["message"], "response": state["response"]})
                return state
            state["attempts"] += 1
            if state["plan"].is_completed():
                final_analysis = await self._analyze_results(state)
                if final_analysis["is_complete"]:
                    state["response"] = final_analysis["response"]
                    state["conversation_history"].append({"user": state["message"], "response": state["response"]})
                    return state
                break

        if not state.get("response"):
            state["response"] = "Tôi không thể xử lý yêu cầu. Vui lòng cung cấp chi tiết cụ thể hơn."
        state["conversation_history"].append({"user": state["message"], "response": state["response"]})
        return state

    def _extract_chat_history_context(self, chat_history: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Extract chat history context in the format expected by the LLM.
        """
        formatted_history = []
        for entry in chat_history:
            if entry["role"] == "user":
                formatted_history.append({"message": entry["content"], "response": ""})
            # elif entry["role"] == "assistant":
            #     if formatted_history and formatted_history[-1]["response"] == "":
            #         formatted_history[-1]["response"] = entry["content"]
        return {"history": formatted_history}
    
    async def _is_casual(self, state: ChatbotState) -> bool:
        casual_prompt = (
            f"{SYSTEM_PROMPT}."
            "You have access to the chat history, including previous interactions with the user.\n\n"
            f"Current question: '{state['message']}'\n\n"      
            "When classifying the current question, consider the chat history to understand the context and intent behind the question.\n"
            "Instructions:\n"
            "1. Determine if the current question is a casual greeting (e.g., 'xin chào', 'hello') or unrelated to internal company data (e.g., 'how's the weather?', 'tell me a joke') when considering the chat history.\n"
            "2. If it is casual or unrelated based on the entire conversation context, respond with exactly 'CASUAL' (nothing else).\n"
            "3. If it relates to internal company data (e.g., tardiness, timesheet, employee info), respond with exactly 'DATA_QUERY' (nothing else).\n"
            "Be very precise in your classification.\n\n"
            "Examples:\n"
            "Example 1:\n"
            "Chat history:\n"
            "- User: Hello, chatbot!\n"
            "- CASUAL\n"
            "Current question: I'm having issues with my timesheet.\n"
            "Classification: DATA_QUERY\n"
            "Explanation: Although the first message was a greeting (CASUAL), the current question relates to internal company information, so it is DATA_QUERY.\n\n"
            "Example 2:\n"
            "Chat history:\n"
            "- User: Do you know any good jokes?\n"
            "- CASUAL\n"
            "- User: Yeah, tell me one.\n"
            "- CASUAL\n"
            "Current question: By the way, how do I submit my timesheet?\n"
            "Classification: DATA_QUERY\n"
            "Explanation: Although previous messages were casual (CASUAL), the current question relates to internal company information, so it is DATA_QUERY.\n\n"
            "Example 3:\n"
            "Chat history:\n"
            "- User: I need help with my tardiness record.\n"
            "- DATA_QUERY\n"
            "Current question: Thanks for that. By the way, do you know any good restaurants nearby?\n"
            "Classification: CASUAL\n"
            "Explanation: The current question is not related to internal company information and is unrelated (CASUAL), even though the previous question was DATA_QUERY."
        )
        response = await self.llm.process_message(state["message"], state["context"], casual_prompt)
        return "CASUAL" in response.strip().upper()

    async def _handle_casual(self, state: ChatbotState) -> str:
        casual_response_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "This has been classified as a casual interaction. Provide a friendly, conversational response.\n"
            "If the user greeted in Vietnamese, respond in Vietnamese. Otherwise respond in English.\n"
            "Keep it brief, friendly and natural."
        )
        return await self.llm.process_message(state["message"], state["context"], casual_response_prompt)

    async def _generate_sql_query(self, state: Dict, metric: str, scope: Dict[str, Any]) -> Tuple[str, List]:
        """
        Generates an SQL query using the LLM based on the metric, scope, and original question.
        Returns a tuple of (query, params).
        """
        schema = """
        Table: employees
        Columns:
        - employee_id: INTEGER PRIMARY KEY
        - name: TEXT NOT NULL
        - email: TEXT NOT NULL UNIQUE
        - hr_code: TEXT NOT NULL
        - start_date: TEXT NOT NULL
        - position: TEXT NOT NULL
        - department: TEXT NOT NULL
        - status: TEXT NOT NULL DEFAULT 'working'
        - employment_type: TEXT NOT NULL
        - years_worked: INTEGER NOT NULL DEFAULT 0
        """

        departments = self.db_handler.execute_query("SELECT DISTINCT department FROM employees")
        valid_departments = [d["department"] for d in departments]

        prompt = f"""
        Generate an SQL query for the SQLite database based on the following information.

        Database Schema:
        {schema}

        Valid Departments: {json.dumps(valid_departments, ensure_ascii=False)}

        Question: '{state["message"]}'
        Metric: {metric}
        Scope: {json.dumps(scope, ensure_ascii=False)}

        Instructions:
        1. Create a valid SQL query to retrieve data **only** from the employees table based on the metric and scope.
        2. Restrict queries to employee data:
           - Only the employees table exists in the database. Do NOT query or assume any other tables (e.g., late_arrivals, payroll, leave).
           - Supported metrics (must use employees table):
             - "employee_ids": Return employee_id, hr_code, and name.
             - "count": Count employees.
             - "list_names": List employee names.
             - "list_details": Return employee_id, name, email, position, department, employment_type, years_worked.
             - "average_years": Average years_worked.
             - "full_time_count": Count employees with employment_type = 'Fulltime chính thức'.
             - "department_list": List distinct departments.
           - If the metric is unsupported (e.g., late_arrivals_count, payroll_data), default to "employee_ids" (returning employee_id, hr_code, and name) and note in error_message: "Metric '[metric]' not supported; defaulting to employee_ids."
           - Ignore question elements unrelated to the employees table (e.g., 'đi muộn', 'late arrivals'); these are handled by agents, not SQL.
        3. Scope handling:
           - "individual": Filter by name or email (use LIKE for partial matches).
           - "department" or "team": Filter by department (use LIKE for partial matches).
           - "position": Filter by position (exact match).
           - "company": No additional filter.
        4. Always include WHERE status = 'working' unless metric is department_list.
        5. Validate department names against valid_departments; if invalid, note in error_message.
        6. Use parameterized queries with ? placeholders for safety.
        7. Return JSON:
           - query: str (the SQL query, must reference only employees table)
           - params: [str] (list of parameter values)
           - error_message: str or null

        Examples:
        Metric: "count"
        Scope: {{"type": "company", "value": ""}}
        Output: {{
            "query": "SELECT COUNT(*) as count FROM employees WHERE status = 'working'",
            "params": [],
            "error_message": null
        }}

        Metric: "employee_ids"
        Scope: {{"type": "department", "value": "Sales"}}
        Output: {{
            "query": "SELECT employee_id, hr_code, name FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
            "params": ["%sales%"],
            "error_message": null
        }}

        Metric: "late_arrivals_count"
        Scope: {{"type": "department", "value": "Phòng nhân sự"}}
        Question: "Tháng này ai đi muộn nhiều nhất Phòng nhân sự?"
        Output: {{
            "query": "SELECT employee_id, hr_code, name FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
            "params": ["%phòng nhân sự%"],
            "error_message": "Metric 'late_arrivals_count' not supported in employees table; defaulting to employee_ids."
        }}

        Metric: "list_details"
        Scope: {{"type": "individual", "value": "Nguyen Van A"}}
        Output: {{
            "query": "SELECT employee_id, name, email, position, department, employment_type, years_worked FROM employees WHERE status = 'working' AND (LOWER(name) LIKE ? OR LOWER(email) LIKE ?)",
            "params": ["%nguyen van a%", "%nguyen van a%"],
            "error_message": null
        }}

        Metric: "count"
        Scope: {{"type": "department", "value": "InvalidDept"}}
        Output: {{
            "query": "SELECT COUNT(*) as count FROM employees WHERE status = 'working' AND LOWER(department) LIKE ?",
            "params": ["%invaliddept%"],
            "error_message": "Department 'InvalidDept' not found in valid departments."
        }}
        """

        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            cleaned_response = response.strip().replace("```json", "").replace("```", "")
            result = json.loads(cleaned_response)
            query = result["query"]
            params = result["params"]
            error_message = result["error_message"]

            try:
                test_result = self.db_handler.execute_query(query, params)
                logger.info(f"Generated query: {query}, params: {params}, rows: {len(test_result)}")
            except Exception as e:
                logger.error(f"Invalid query: {query}, error: {e}")
                raise ValueError(f"Generated query is invalid: {e}")

            if error_message:
                logger.warning(f"Query warning: {error_message}")

            return query, params
        except Exception as e:
            logger.error(f"Failed to generate SQL query: {e}")
            raise ValueError(f"Cannot generate SQL query: {e}")


    async def _can_execute(self, step, executed_steps):
        dependencies = step.get("depends_on", [])
        return all(dep in executed_steps for dep in dependencies)

    async def _handle_step_failure(self, state, step):
        contingency = state["plan"].get_contingency(step["id"])
        if contingency:
            await self._execute_contingency(state, contingency)
        else:
            state["response"] = f"Step {step['id']} failed."

    async def _execute_contingency(self, state, contingency):
        for action in contingency:
            if action["action"] == "ask_user":
                state["response"] = action["prompt"]
                break

    async def _execute_plan(self, state: ChatbotState):
        executed_steps = set()

        while not state["plan"].is_completed():
            current_step = state["plan"].get_current_step()
            if not current_step:
                state["response"] = "No more steps to execute in the plan."
                break

            step_id = current_step["id"]
            if await self._can_execute(current_step, executed_steps):
                result = await self._execute_step(state, current_step)
                if result["success"]:
                    executed_steps.add(step_id)
                    # analysis = await self.assistant.analyze_step_result(state, step_id)
                    # if not analysis["is_valid"]:
                    #     await self._handle_step_failure(state, current_step)
                    #     if state.get("response"):
                    #         break
                else:
                    await self._handle_step_failure(state, current_step)
                    if state.get("response"):
                        break
            else:
                state["response"] = f"Cannot execute step {step_id}: dependencies not met."
                break

            state["plan"].advance()

        if state.get("response"):
            return

        if state["plan"].is_completed() and not state.get("response"):
            final_analysis = await self._analyze_results(state)
            state["response"] = final_analysis["response"]

    async def _execute_step(self, state, step):
        action = step["action"]
        try:
            if action == "query_db":
                metric = step["attributes"]["metric"]
                scope = step["attributes"]["scope"]
                query, params = await self._generate_sql_query(state, metric, scope)
                result_data = self.db_handler.execute_query(query, params)
                if 'call_agent' in [action['action'] for action in  state['plan'].steps]:
                    employee_info = [
                        {"employee_id": str(emp["employee_id"]), "hr_code": emp["hr_code"], "name": emp["name"]}
                        for emp in result_data
                    ]
                    state["employee_ids"] = [emp["employee_id"] for emp in employee_info]
                    state["employee_names"] = [emp["name"] for emp in employee_info]
                    state["employee_info"] = employee_info

                    state["agent_results"][step["id"]] = {
                        "response": employee_info,
                        "status": "success",
                        "data": result_data,
                        "agent_name": "statistics"
                    }
                    return {"success": True}

                predefined_metrics = [
                    "count", "list_names", "list_details", "average_years",
                    "full_time_count", "department_list", "employee_ids"
                ]
                if metric in predefined_metrics:
                    result = self.stats_processor.process_statistics(result_data, metric, scope)
                    state["agent_results"][step["id"]] = {
                        "response": result["response"],
                        "status": "success",
                        "data": result["data"],
                        "agent_name": "statistics"
                    }
                    state["response"] = await self._process_db_result(state, result, metric, scope)
                else:
                    result = {"response": None, "data": result_data}
                    state["agent_results"][step["id"]] = {
                        "response": None,
                        "status": "success",
                        "data": result_data,
                        "agent_name": "statistics"
                    }
                    state["response"] = await self._process_db_result(state, result, metric, scope)
                return {"success": True}
            elif action == "get_employee_ids":
                scope = step["attributes"]["scope"]
                query, params = await self._generate_sql_query(state, "employee_ids", scope)
                employees = self.db_handler.execute_query(query, params)
                state["employee_ids"] = [str(emp["employee_id"]) for emp in employees]
                state["agent_results"][step["id"]] = {
                    "response": f"Found {len(employees)} employees",
                    "status": "success",
                    "data": {"employee_ids": state["employee_ids"]},
                    "agent_name": "get_employee_ids"
                }
                return {"success": bool(state["employee_ids"])}
            elif action == "extract_time":
                state = await self._extract_time(state)
                success = bool(state["entities"].get("time_period"))
                if success:
                    state["agent_results"][step["id"]] = {
                        "response": f"Time period: {state['entities']['time_period']}",
                        "status": "success",
                        "data": state["entities"]["time_period"],
                        "agent_name": "extract_time"
                    }
                else:
                    state["agent_results"][step["id"]] = {
                        "response": "Failed to extract time period",
                        "status": "error",
                        "data": {},
                        "agent_name": "extract_time"
                    }
                return {"success": success}
            elif action == "call_agent":
                agent_name = step["attributes"]["agent_name"]
                step_id = step["id"]
                await self._execute_agent(state, agent_name, step_id)
                success = state["agent_results"].get(step_id, {}).get("status") == "success"
                return {"success": success}
            else:
                raise ValueError(f"Unknown action: {action}")
        except Exception as e:
            state["error_message"] = f"Error in step {step['id']}: {e}"
            state["agent_results"][step["id"]] = {
                "response": str(e),
                "status": "error",
                "data": {},
                "agent_name": action
            }
            return {"success": False}


    async def _process_db_result(self, state, result, metric, scope):
        scope_description = self.stats_processor._get_scope_description(scope)
        prompt = f"""
        Dựa trên câu hỏi: '{state['message']}'
        Dữ liệu thống kê:
        - Metric: {metric}
        - Kết quả: {json.dumps(result['data'], ensure_ascii=False)}
        - Phạm vi: {scope_description}
        Tạo một câu trả lời tự nhiên, thân thiện bằng tiếng Việt, đúng với ngữ cảnh câu hỏi.
        Đảm bảo câu trả lời chi tiết, rõ ràng với yêu cầu của người dùng.
        """
        try:
            return await self.llm.process_message(state["message"], state["context"], prompt)
        except Exception as e:
            logger.error(f"LLM response error: {e}")
            return result["response"]

    async def _extract_time(self, state: ChatbotState) -> ChatbotState:
        vietnam_tz = pytz.timezone('Asia/Ho_Chi_Minh')
        current_date = datetime.now(vietnam_tz)
        period = state["plan"].get_current_step()["attributes"]["period"]

        def is_leap_year(year: int) -> bool:
            return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

        def get_month_end(year: int, month: int) -> int:
            if month in [4, 6, 9, 11]:
                return 30
            elif month == 2:
                return 29 if is_leap_year(year) else 28
            else:
                return 31

        def parse_clear_period(period: str, current: datetime) -> dict:
            period = period.strip().lower()

            patterns = [
                r'^hôm nay$',
                r'^hôm qua$',
                r'^ngày mai$',
                r'^tháng này$',
                r'^tháng trước$',
                r'^tháng sau$',
                r'^tháng\s*(\d{1,2})$',
                r'^tháng\s*(\d{1,2})\s*năm\s*(\d{4})$',
                r'^năm nay$',
                r'^năm trước$',
                r'^năm sau$',
                r'^năm\s*(\d{4})$'
            ]

            for pattern in patterns:
                match = re.match(pattern, period)
                if match:
                    if pattern == r'^hôm nay$':
                        return {
                            "start_date": current.strftime("%Y-%m-%d"),
                            "end_date": current.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^hôm qua$':
                        date = current - timedelta(days=1)
                        return {
                            "start_date": date.strftime("%Y-%m-%d"),
                            "end_date": date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^ngày mai$':
                        date = current + timedelta(days=1)
                        return {
                            "start_date": date.strftime("%Y-%m-%d"),
                            "end_date": date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^tháng này$':
                        start_date = current.replace(day=1)
                        end_date = start_date.replace(day=get_month_end(current.year, current.month))
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^tháng trước$':
                        start_date = (current.replace(day=1) - relativedelta(months=1))
                        end_date = start_date.replace(day=get_month_end(start_date.year, start_date.month))
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^tháng sau$':
                        start_date = (current.replace(day=1) + relativedelta(months=1))
                        end_date = start_date.replace(day=get_month_end(start_date.year, start_date.month))
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^tháng\s*(\d{1,2})$':
                        month = int(match.group(1))
                        if 1 <= month <= 12:
                            year = current.year
                            if month > current.month:
                                year -= 1
                            start_date = current.replace(year=year, month=month, day=1)
                            end_date = start_date.replace(day=get_month_end(year, month))
                            return {
                                "start_date": start_date.strftime("%Y-%m-%d"),
                                "end_date": end_date.strftime("%Y-%m-%d")
                            }

                    elif pattern == r'^tháng\s*(\d{1,2})\s*năm\s*(\d{4})$':
                        month = int(match.group(1))
                        year = int(match.group(2))
                        if 1 <= month <= 12:
                            start_date = current.replace(year=year, month=month, day=1)
                            end_date = start_date.replace(day=get_month_end(year, month))
                            return {
                                "start_date": start_date.strftime("%Y-%m-%d"),
                                "end_date": end_date.strftime("%Y-%m-%d")
                            }
                    elif pattern == r'^năm nay$':
                        start_date = current.replace(month=1, day=1)
                        end_date = current.replace(month=12, day=31)
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^năm trước$':
                        year = current.year - 1
                        start_date = current.replace(year=year, month=1, day=1)
                        end_date = current.replace(year=year, month=12, day=31)
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^năm sau$':
                        year = current.year + 1
                        start_date = current.replace(year=year, month=1, day=1)
                        end_date = current.replace(year=year, month=12, day=31)
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }
                    elif pattern == r'^năm\s*(\d{4})$':
                        year = int(match.group(1))
                        start_date = current.replace(year=year, month=1, day=1)
                        end_date = current.replace(year=year, month=12, day=31)
                        return {
                            "start_date": start_date.strftime("%Y-%m-%d"),
                            "end_date": end_date.strftime("%Y-%m-%d")
                        }

            return None

        result = parse_clear_period(period, current_date)
        if result:
            state["entities"]["time_period"] = result
            return state

        prompt = f"""
        You are a Vietnamese time parser. Extract precise "start_date" and "end_date" in "YYYY-MM-DD" format based on the input.

        Current date: {current_date}
        Input: "{period}"

        Return JSON only:
        - Valid: {{"start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD"}}
        - Invalid: {{"start_date": null, "end_date": null, "error": "unclear"}}

        Vietnamese expressions (ALWAYS considered valid unless marked ambiguous):
        - Day:
          - "hôm nay" → today = {current_date}
          - "hôm qua" → {current_date} - 1 day
          - "ngày mai" → {current_date} + 1 day
        - Month:
          - "tháng này" → full current month
          - "tháng trước" → previous month
          - "tháng sau" → next month
          - "tháng X" → month X in current year (if X > current month, use last year)
          - "tháng X năm YYYY" → month X in year YYYY
        - Year:
          - "năm nay" → Jan 1 to Dec 31 of current year
          - "năm trước" → Jan 1 to Dec 31 of last year
          - "năm sau" → Jan 1 to Dec 31 of next year
          - "năm YYYY" → full year YYYY

        Rules:
        - Always use accurate number of days per month (28/29/30/31)
        - Leap year: Feb = 29 days if divisible by 4 (except century years not divisible by 400)
        - Ambiguous examples (return error): "mấy ngày trước", "tuần tới", "gần đây", etc.
        - Ensure start_date ≤ end_date
        - Output: JSON only, no explanation
        """

        print("Prompt time", prompt)
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            print("Response", response)
            cleaned = response.strip().replace("```json", "").replace("```", "").strip()
            if cleaned.startswith("{{") and cleaned.endswith("}}"):
                cleaned = cleaned[1:-1]
            elif cleaned.startswith("{") and cleaned.endswith("}"):
                pass
            else:
                cleaned = f"{{{cleaned}}}"
            time_period = json.loads(cleaned.strip())
            start_date = time_period.get("start_date")
            end_date = time_period.get("end_date")
            error = time_period.get("error")
            if error == "unclear" or not (start_date and end_date):
                state["response"] = (
                    f"Tôi không thể xác định chính xác thời gian '{period}'. "
                    f"Bạn có thể cung cấp thời gian cụ thể hơn không (ví dụ: {current_date})?"
                )
                return state
            try:
                datetime.strptime(start_date, "%Y-%m-%d")
                datetime.strptime(end_date, "%Y-%m-%d")
                if start_date > end_date:
                    start_date, end_date = end_date, start_date
                state["entities"]["time_period"] = {"start_date": start_date, "end_date": end_date}
            except ValueError:
                state["response"] = (
                    f"Thời gian '{period}' được phân tích không hợp lệ. "
                    "Vui lòng cung cấp thời gian rõ ràng hơn (ví dụ: 'tháng 4/2025' hoặc 'hôm qua')."
                )
                return state
            return state
        except Exception as e:
            logger.error(f"Error extracting time: {str(e)}")
            state["response"] = (
                f"Tôi không thể hiểu thời gian '{period}' do lỗi xử lý. "
                "Vui lòng cung cấp thời gian cụ thể hơn (ví dụ: 'tháng 4/2025' hoặc 'ngày 8/4/2025')."
            )
            return state

    async def _execute_agent(self, state: ChatbotState, agent_name: str, step_id: str) -> None:
        subdomain = next((s for s in self.sub_agents if agent_name in self.sub_agents[s]), state["subdomains"][0])
        agent = self.sub_agents.get(subdomain, {}).get(agent_name).get('instance')
        if not agent:
            state["agent_results"][step_id] = {"response": None, "status": "error", "error": "Agent not found"}
            return

        try:
            result = await agent.execute(state, self.tools)
            state["agent_results"][step_id] = {"response": result, "status": "success", "error": None}
        except Exception as e:
            state["agent_results"][step_id] = {"response": None, "status": "error", "error": str(e)}

    async def _analyze_results(self, state: ChatbotState) -> Dict:
        prompt = (
            f"Question: '{state['message']}'\n"
            f"Current results: {json.dumps(state['agent_results'], indent=2)}\n"
            f"Entities extracted: {json.dumps(state['entities'], indent=2)}\n"
            f"Execution plan: {json.dumps({'steps': state['plan'].steps}, indent=2)}\n"
            "Analyze:\n"
            "1. Are the results sufficient to answer the question? (yes/no)\n"
            "   - Check if all required data is present in 'agent_results' based on the question and plan.\n"
            "   - Each result is stored by step ID (e.g., 'agent1', 'agent2') with 'agent_name', 'response', 'status', and 'error'.\n"
            "2. If yes, combine the results into a final response:\n"
            "   - Use the 'response' field from each successful step.\n"
            "   - Include context from 'entities' (e.g., time_period) and link to step IDs in the plan.\n"
            "   - Example: 'Ngô Gia Phong was late on 2025-04-01 (agent1), check-in at 08:30 (agent2)'.\n"
            "3. If no, identify what's missing or wrong:\n"
            "   - Missing data: e.g., 'no result for agent3', 'employee_id not set'.\n"
            "   - Errors: Check 'status' == 'error' in results and report the 'error' field.\n"
            "4. Suggest next steps if needed:\n"
            "   - Examples: 'get_employee_id', 'call_agent:tardiness with period=tháng 3', 'retry_agent:agent3'.\n"
            "Return JSON: {{'response': str, 'is_complete': bool, 'next_steps': [str]}}"
            "*** Format naturally in Vietnamese, ensuring all relevant details are mentioned."
        )
        
        # Convert chat history to the format expected by LLM
        chat_history = []
        for entry in state.get("chat_history", []):
            if entry["role"] == "user":
                chat_history.append({"message": entry["content"], "response": ""})
            elif entry["role"] == "assistant":
                if chat_history and chat_history[-1]["response"] == "":
                    chat_history[-1]["response"] = entry["content"]
        
        # Create context with chat history
        context = {"history": chat_history}
        
        response = await self.llm.process_message(state["message"], context, prompt)
        return json.loads(format_json_response(response))

    async def _replan(self, state: ChatbotState, next_steps: List[str]) -> Plan:
        new_steps = []
        new_concurrent_steps = []
        for step in next_steps:
            if "call_agent" in step:
                agent_name = step.split(":")[1] if ":" in step else "tardiness"
                new_concurrent_steps.append([{"action": "call_agent", "agent_name": agent_name}])
            elif step in ["get_employee_id", "extract_time"]:
                new_steps.append({"action": step})
        return Plan(steps=new_steps, concurrent_steps=new_concurrent_steps)
        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            return json.loads(response.strip().replace("```json", "").replace("```", ""))
        except Exception as e:
            logger.error(f"Final analysis failed: {str(e)}")
            return {"response": "Error in final analysis", "is_complete": False, "next_steps": []}

    def get_list_tools_domain(self, subdomains: list) -> Dict:
        tools = {}
        for subdomain in subdomains:
            tools[subdomain] = []
            apis_subdomain_dir = os.path.join(self.domains_dir, self.domain, subdomain, "apis")
            if os.path.isdir(apis_subdomain_dir):
                for api_domain in os.listdir(apis_subdomain_dir):
                    with open(os.path.join(apis_subdomain_dir, api_domain), "r") as f:
                        data_api = json.load(f)
                    tools[subdomain].append(data_api)
        return tools

    async def process(self, message: str, user_id: str, chat_history: Optional[List[Dict[str, str]]] = None) -> str:
        """
        Process user message with chat history context.
        
        Args:
            message: The current user message
            user_id: The user ID
            chat_history: List of previous messages in format [{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
            
        Returns:
            The response message
        """
        # Initialize chat history if not provided
        if chat_history is None:
            chat_history = []
        # Filter chat history to include only user messages
        chat_history = [entry for entry in chat_history if entry["role"] == "user"]

        # Create initial state
        state = ChatbotState(
            user_id=user_id,
            domain="hr",
            message=message,
            context={},
            subdomains=[],
            entities={},
            agent_results={},
            response=None,
            error_message=None,
            retry_count={},
            employee_id=None,
            attempts=0,
            dependencies={},
            chat_history=[],
        )
        
        # Update context with chat history
        if chat_history:
            # Extract relevant information from chat history
            context_prompt = (
                f"Analyze the following chat history and extract relevant information:\n"
                f"{json.dumps(chat_history, indent=2)}\n\n"
                f"Extract:\n"
                f"- Employee names mentioned\n"
                f"- Time periods mentioned\n"
                f"- Subdomains discussed (attendance, payroll, leave, performance)\n"
                f"Return ONLY JSON string with keys: employee_names, time_periods, subdomains"
            )
            
            try:
                context_response = await self.llm.process_message(message, {}, context_prompt)
                context_data = json.loads(format_json_response(context_response))
                
                # Update state with extracted context
                if context_data.get("employee_names"):
                    if state["entities"].get("employee_names"):
                        state["entities"]["employee_names"].extend(context_data["employee_names"])
                    else:
                        state["entities"]["employee_names"] = context_data["employee_names"]
                
                if context_data.get("time_periods"):
                    if state["entities"].get("time_periods"):
                        state["entities"]["time_periods"].extend(context_data["time_periods"])
                    else:
                        state["entities"]["time_periods"] = context_data["time_periods"]
                
                if "subdomains" in context_data and context_data["subdomains"]:
                    state["subdomains"].extend(context_data["subdomains"])
                    
            except Exception as e:
                logger.warning(f"Failed to extract context from chat history: {str(e)}")
        
        # Process the state through the graph
        result = await self.graph.ainvoke(state)
        
        # Return only the response, chat history is managed by ChatbotCore
        return result["response"]