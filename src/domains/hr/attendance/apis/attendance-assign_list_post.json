{"type": "function", "function": {"name": "attendance-assign_list_post", "description": "Danh sách phân ca - This API retrieves a list of attendance assignments, such as shifts assigned to departments or employees. It is used for managing shift assignments and ensuring employees are scheduled correctly. The API supports pagination, searching by name or code, and sorting (e.g., by last updated time). Use this API when you need to view shift assignments for a department or specific employees. The response includes assignment details such as department, employee, and shift.", "parameters": {"type": "object", "properties": {"query": {"type": "object", "description": "Parameter query", "properties": {"type": {"type": "string", "description": "Sub-parameter of query"}}, "required": ["type"]}, "search": {"type": "string", "description": "Parameter search", "default": ""}, "page": {"type": "integer", "description": "Parameter page", "default": 1}, "total": {"type": "integer", "description": "Parameter total", "default": 0}, "totalPages": {"type": "integer", "description": "Parameter totalPages", "default": 0}, "pageSize": {"type": "integer", "description": "Parameter pageSize", "default": 20}, "searchFields": {"type": "array", "description": "Parameter searchFields", "default": ["name", "code"], "items": {"type": "string"}}, "sort": {"type": "array", "description": "Parameter sort", "default": ["-updatedAt"], "items": {"type": "string"}}, "populates": {"type": "array", "description": "Parameter populates", "default": [], "items": {"type": "string"}}}, "required": ["query", "search", "page", "total", "totalPages", "pageSize", "searchFields", "sort", "populates"], "additionalProperties": false}, "path": "/attendance-assign/list", "method": "POST", "example": {"query": {"type": "department"}, "search": "", "page": 1, "total": 0, "totalPages": 0, "pageSize": 20, "searchFields": ["name", "code"], "sort": ["-updatedAt"], "populates": []}}}