{"type": "function", "function": {"name": "employee_list_post", "description": "<PERSON><PERSON> s<PERSON>ch nhân sự - This API retrieves a list of employees in the company based on specified filters. It is used for employee directory lookups or to find employees by name, HR code, or email. The API supports pagination and sorting, and can filter employees by status (e.g., 'working'). Use this API when you need to search for an employee (e.g., to resolve a name to an employee ID) or list all employees. The response includes employee details such as ID, name, and email.", "parameters": {"type": "object", "properties": {"query": {"type": "object", "description": "Parameter query", "properties": {"status": {"type": "string", "description": "Sub-parameter of query"}}, "required": ["status"]}, "search": {"type": "string", "description": "Parameter search", "default": ""}, "page": {"type": "integer", "description": "Parameter page", "default": 1}, "total": {"type": "integer", "description": "Parameter total", "default": 0}, "totalPages": {"type": "integer", "description": "Parameter totalPages", "default": 0}, "pageSize": {"type": "integer", "description": "Parameter pageSize", "default": 50}, "searchFields": {"type": "array", "description": "Parameter searchFields", "default": ["hr_code", "name", "email"], "items": {"type": "string"}}, "sort": {"type": "array", "description": "Parameter sort", "default": ["hr_code_sort"], "items": {"type": "string"}}, "status": {"type": "string", "description": "Parameter status", "default": "working"}}, "required": ["query", "search", "page", "total", "totalPages", "pageSize", "searchFields", "sort", "status"], "additionalProperties": false}, "path": "/employee/list", "method": "POST", "example": {"query": {"status": "working"}, "search": "", "page": 1, "total": 0, "totalPages": 0, "pageSize": 50, "searchFields": ["hr_code", "name", "email"], "sort": ["hr_code_sort"], "status": "working"}}}