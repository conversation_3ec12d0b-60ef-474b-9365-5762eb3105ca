{"type": "function", "function": {"name": "shift_list_post", "description": "<PERSON><PERSON> s<PERSON>ch ca làm việc - This API retrieves a list of work shifts available in the company. It is used for scheduling purposes, such as viewing shift details or planning employee schedules. The API supports pagination, searching by shift name, and sorting. Use this API when you need to list all shifts or find specific shifts (e.g., by name). The response includes shift details such as name and schedule.", "parameters": {"type": "object", "properties": {"query": {"type": "object", "description": "Parameter query", "properties": {}, "required": []}, "search": {"type": "string", "description": "Parameter search", "default": ""}, "page": {"type": "integer", "description": "Parameter page", "default": 1}, "total": {"type": "integer", "description": "Parameter total", "default": 0}, "totalPages": {"type": "integer", "description": "Parameter totalPages", "default": 0}, "pageSize": {"type": "integer", "description": "Parameter pageSize", "default": 20}, "searchFields": {"type": "array", "description": "Parameter searchFields", "default": ["name"], "items": {"type": "string"}}, "sort": {"type": "array", "description": "Parameter sort", "default": [], "items": {"type": "string"}}, "populates": {"type": "array", "description": "Parameter populates", "default": [], "items": {"type": "string"}}}, "required": ["query", "search", "page", "total", "totalPages", "pageSize", "searchFields", "sort", "populates"], "additionalProperties": false}, "path": "/shift/list", "method": "POST", "example": {"query": {}, "search": "", "page": 1, "total": 0, "totalPages": 0, "pageSize": 20, "searchFields": ["name"], "sort": [], "populates": []}}}