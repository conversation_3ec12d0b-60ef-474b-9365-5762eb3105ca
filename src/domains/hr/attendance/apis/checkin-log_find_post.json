{"type": "function", "function": {"name": "checkin-log_find_post", "description": "<PERSON><PERSON> liệu chấm công (chi tiết) - This API retrieves detailed check-in and check-out logs for a specific employee within a given time range. It is used for attendance tracking, such as monitoring an employee's check-in/check-out times or calculating attendance metrics. The API requires an employee ID, company ID, and a date range (e.g., March 2025). Use this API when you need to view detailed attendance records for an employee over a specific period. The response includes logs with timestamps and types (e.g., 'checkin', 'checkout').", "parameters": {"type": "object", "properties": {"query": {"type": "object", "description": "Parameter query", "properties": {"employee_id": {"type": "integer", "description": "Sub-parameter of query"}, "company_id": {"type": "integer", "description": "Sub-parameter of query"}, "type": {"type": "object", "description": "Sub-parameter of query", "properties": {"$in": {"type": "array", "items": {"type": "string"}, "default": ["checkin", "checkout", "import"]}}, "required": ["$in"]}, "datetime": {"type": "object", "description": "Sub-parameter of query", "properties": {"$gte": {"type": "string", "format": "date-time", "default": "2025-03-03T17:00:00.000Z"}, "$lte": {"type": "string", "format": "date-time", "default": "2025-03-04T16:59:59.999Z"}}, "required": ["$gte", "$lte"]}}, "required": ["employee_id", "company_id", "type", "datetime"]}, "sort": {"type": "array", "description": "Parameter sort", "default": ["datetime"], "items": {"type": "string"}}, "addCompanyUtcOffsetToQueryDatetime": {"type": "integer", "description": "Parameter addCompanyUtcOffsetToQueryDatetime", "default": true}}, "required": ["query", "sort", "addCompanyUtcOffsetToQueryDatetime"], "additionalProperties": false}, "path": "/checkin-log/find", "method": "POST", "example": {"query": {"employee_id": 3, "company_id": 1, "type": {"$in": ["checkin", "checkout", "import"]}, "datetime": {"$gte": "2025-03-03T17:00:00.000Z", "$lte": "2025-03-04T16:59:59.999Z"}}, "sort": ["datetime"], "addCompanyUtcOffsetToQueryDatetime": true}}}