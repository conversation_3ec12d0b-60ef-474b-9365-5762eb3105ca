{"type": "function", "function": {"name": "employee_list-with-timesheet_post", "description": "Bảng công - This API retrieves a list of employees along with their timesheet data for a specific month and year. It is used for generating attendance reports or calculating work hours for payroll purposes. The API supports pagination, searching by HR code, name, or email, and sorting. Use this API when you need to view timesheet data for employees over a specific period (e.g., March 2025). The response includes employee details and their timesheet entries for the specified month.", "parameters": {"type": "object", "properties": {"query": {"type": "object", "description": "Parameter query", "properties": {"status": {"type": "string", "description": "Sub-parameter of query"}}, "required": ["status"]}, "search": {"type": "string", "description": "Parameter search", "default": ""}, "page": {"type": "integer", "description": "Parameter page", "default": 1}, "total": {"type": "integer", "description": "Parameter total", "default": 0}, "totalPages": {"type": "integer", "description": "Parameter totalPages", "default": 0}, "pageSize": {"type": "integer", "description": "Parameter pageSize", "default": 10}, "searchFields": {"type": "array", "description": "Parameter searchFields", "default": ["hr_code", "name", "uid", "email", "phone"], "items": {"type": "string"}}, "sort": {"type": "array", "description": "Parameter sort", "default": ["hr_code_sort"], "items": {"type": "string"}}, "populates": {"type": "array", "description": "Parameter populates", "default": [], "items": {"type": "string"}}, "month": {"type": "integer", "description": "Parameter month", "default": 2}, "year": {"type": "integer", "description": "Parameter year", "default": 2025}}, "required": ["query", "search", "page", "total", "totalPages", "pageSize", "searchFields", "sort", "populates", "month", "year"], "additionalProperties": false}, "path": "/employee/list-with-timesheet", "method": "POST", "example": {"query": {"status": "working"}, "search": "", "page": 1, "total": 0, "totalPages": 0, "pageSize": 10, "searchFields": ["hr_code", "name", "uid", "email", "phone"], "sort": ["hr_code_sort"], "populates": [], "month": 2, "year": 2025}}}