import asyncio
import json
import logging
import re
from calendar import monthrange
from datetime import datetime, timedelta
from typing import Dict, List

import aiocache

from src.modules.function_caller import FunctionCaller

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize cache
cache = aiocache.SimpleMemoryCache()


def validate_llm_response(response: str) -> bool:
    """
    Validates if the LLM response is a properly formatted JSON.

    Args:
        response (str): The response string from LLM

    Returns:
        bool: True if the response is valid JSON, False otherwise
    """
    # Check if response is already a valid JSON
    try:
        json.loads(response)
        return True
    except json.JSONDecodeError:
        return False


def format_json_response(response: str) -> str:
    json_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
    json_blocks = re.findall(json_block_pattern, response)

    if json_blocks:
        for block in json_blocks:
            try:
                parsed_json = json.loads(block)
                return json.dumps(parsed_json, indent=2, ensure_ascii=False)
            except json.JSONDecodeError:
                continue

    json_pattern = r"\{[\s\S]*\}"
    json_matches = re.findall(json_pattern, response)

    if json_matches:
        for match in json_matches:
            try:
                parsed_json = json.loads(match)
                return json.dumps(parsed_json, indent=2, ensure_ascii=False)
            except json.JSONDecodeError:
                continue

    try:
        fixed_response = re.sub(r"'([^']*)':\s*", r'"\1": ', response)
        fixed_response = re.sub(r"'([^']*)'", r'"\1"', fixed_response)
        fixed_response = re.sub(r",\s*}", "}", fixed_response)
        fixed_response = re.sub(r",\s*\]", "]", fixed_response)
        fixed_response = re.sub(r'"search":\s*"([^"])"([^"]+)"', r'"search": "\1\2"', fixed_response)
        parsed_json = json.loads(fixed_response)

        if "search" in parsed_json and isinstance(parsed_json["search"], str):
            parsed_json["search"] = parsed_json["search"].replace('"', '')

        return json.dumps(parsed_json, indent=2, ensure_ascii=False)

    except json.JSONDecodeError:
        logger.warning("Failed to fix JSON formatting.")

    try:
        kv_pattern = r"[\"']?([^:\"']+)[\"']?\s*:\s*[\"']?([^,\{\}\[\]\"']+)[\"']?"
        kv_pairs = re.findall(kv_pattern, response)

        if kv_pairs:
            result = {}
            for key, value in kv_pairs:
                key = key.strip()
                value = value.strip()

                try:
                    if '.' in value:
                        value = float(value)
                    else:
                        value = int(value)
                except ValueError:
                    pass

                result[key] = value

            return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Failed to extract structured data: {str(e)}")

    return response


async def execute_with_retry(function_caller: FunctionCaller, function_data: Dict, max_retries: int = 3) -> Dict:
    """Execute an API call with retry logic."""
    # Create a cache key based on the function and parameters
    cache_key = f"api_call:{function_data['function']}:{str(function_data['params'])}"
    cached_result = await cache.get(cache_key)
    if cached_result:
        logger.info(f"Cache hit for {cache_key}")
        return cached_result

    for attempt in range(max_retries):
        try:
            result = await function_caller.execute(function_data)
            if "error" not in result:
                await cache.set(cache_key, result, ttl=3600)  # Cache for 1 hour
                return result
            logger.warning(f"API call failed (attempt {attempt + 1}/{max_retries}): {result['error']}")
        except Exception as e:
            logger.warning(f"API call failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
        if attempt < max_retries - 1:
            await asyncio.sleep(1)
    error_result = {"error": "Failed to execute API call after maximum retries"}
    await cache.set(cache_key, error_result, ttl=300)  # Cache errors for 5 minutes
    return error_result


def populate_params_with_previous_results(function_data: Dict, api_results: List[List[Dict]]) -> Dict:
    """Populate function parameters with data from previous API results."""
    params = function_data["params"]
    for key, value in params.items():
        if isinstance(value, str) and value.startswith("from_previous:"):
            field = value.split(":")[1]
            for step_results in reversed(api_results):
                for result in step_results:
                    if isinstance(result, dict) and field in result:
                        params[key] = result[field]
                        break
                if params[key] != value:
                    break
    return function_data


def _filter_data(data: List[Dict], entities: Dict) -> List[Dict]:
    """Filter API data based on entities."""
    if not data or not isinstance(data, list):
        return []

    filtered_data = data
    if "employee_name" in entities or "employee_email" in entities:
        filtered_data = [
            item for item in filtered_data
            if item.get("name") == entities.get("employee_name") or item.get("email") == entities.get("employee_email")
        ]
    if "month" in entities and "year" in entities:
        filtered_data = [
            item for item in filtered_data
            if "date" in item and item["date"].startswith(f"{entities['year']}-{entities['month']:02d}")
        ]
    return filtered_data


def _summarize_data(data: List[Dict], intent: str, entities: Dict) -> Dict:
    """Summarize large API data based on intent and entities."""
    if not data:
        return {"summary": "No data to summarize."}

    summary = {}
    if intent == "get_missed_days":
        missed_days = {
                          f"{entities['year']}-{entities['month']:02d}-{day:02d}"
                          for day in range(1, monthrange(entities["year"], entities["month"])[1] + 1)
                      } - {item["date"] for item in data if item.get("type") == "work"}
        summary["missed_days"] = len(missed_days)
    elif intent == "get_work_hours":
        total_hours = sum(item["shift"]["total_hour"] for item in data if item.get("type") == "work")
        summary["total_hours"] = total_hours
    elif intent == "get_leave_days":
        remaining_days = data[0].get("remaining_days", 0) if data else 0
        summary["remaining_leave_days"] = remaining_days
    else:
        top_items = data[:5]
        summary["top_items"] = top_items
        if len(data) > 5:
            summary["additional_count"] = len(data) - 5
    return summary


def process_api_results(state):
    """Process API results generically."""
    current_step = state["current_step"]
    if not state["api_results"][current_step]:
        return state

    intent = state["intents"][current_step] if current_step < len(state["intents"]) else "unknown"
    entities = state["entities"]
    api_results = state["api_results"][current_step]
    analyzed_result = {}

    try:
        for result in api_results:
            if "error" in result:
                analyzed_result["error"] = result["error"]
                continue

            data = result.get("data", [])
            if not isinstance(data, list):
                data = [data]

            filtered_data = _filter_data(data, entities)
            if len(filtered_data) > 5:
                summarized_data = _summarize_data(filtered_data, intent, entities)
                analyzed_result.update(summarized_data)
            else:
                analyzed_result.update({"filtered_data": filtered_data})

    except Exception as e:
        logger.error(f"Error processing API result for intent {intent}: {str(e)}")
        analyzed_result = {"error": str(e)}

    state["aggregated_results"][intent] = analyzed_result
    return state


def generate_response(state, domain: str):
    """Generate a natural response from aggregated results."""
    if state["response"]:
        return state
    try:
        aggregated_results = state["aggregated_results"]
        response_parts = []
        for intent, result in aggregated_results.items():
            if "error" in result:
                response_parts.append(f"Error processing {intent}: {result['error']}")
            elif intent == "get_missed_days":
                response_parts.append(f"Nhân viên đã nghỉ {result['missed_days']} ngày trong tháng.")
            elif intent == "get_work_hours":
                response_parts.append(f"Tổng số giờ làm việc là {result['total_hours']} giờ.")
            elif intent == "get_leave_days":
                response_parts.append(f"Số ngày nghỉ phép còn lại là {result['remaining_leave_days']} ngày.")
            else:
                if "top_items" in result:
                    items = [str(item) for item in result["top_items"]]
                    response = f"{', '.join(items)}"
                    if "additional_count" in result:
                        response += f" và {result['additional_count']} mục khác."
                    response_parts.append(response)
                elif "filtered_data" in result:
                    response_parts.append(f"{result['filtered_data']}")
                else:
                    response_parts.append(f"Không có thông tin chi tiết cho {intent}.")
        state["response"] = " ".join(response_parts) if response_parts else "Không có thông tin để trả lời."
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        state["response"] = f"Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu: {str(e)}"
    return state


def safe_get(d, *keys):
    for key in keys:
        if isinstance(d, dict) and key in d:
            d = d[key]
        else:
            return None
    return d


def process_department(departments: list):
    department_results = []
    for department in departments:
        department_name, position = "", ""
        if 'directory' in department and department["directory"] and "name" in department["directory"]:
            department_name = department["directory"]["name"]
        if "designation" in department and department["designation"] and "name" in department["designation"]:
            position = department["designation"]["name"]
        department_results.append({
            "department": department_name,
            "position": position
        })
    return department_results


def process_profile_result(profiles: list):
    results = []
    for profile in profiles:
        profile_str = f"""- {profile['name']}:
    Email: {profile['email']}
    Ngày bắt đầu: {profile['start_date']}
    Ngày bắt đầu thử việc: {profile['probationary_start_date']}
    Ngày chính thức: {profile['official_start_date']}
    Bộ phận:"""
        for department in profile["department"]:
            profile_str += f"""
        + {department['department']}
            Chức danh: {department['position']}"""
        results.append(profile_str)
    return "\n".join(results) if results else "không có nhân viên nào"


def get_total_year_time_periods():
    current_date = datetime.now()
    current_year = current_date.year
    current_month = current_date.month

    time_period = {}

    # Khởi tạo dict theo năm
    year_key = "từ đầu năm đến giờ"
    time_period[year_key] = {}

    # Duyệt qua từng tháng từ tháng 1 đến tháng hiện tại
    for month in range(1, current_month + 1):
        # Ngày bắt đầu của tháng
        start_date = datetime(current_year, month, 1)
        # Ngày bắt đầu của tháng kế tiếp (dùng để tính ngày cuối tháng hiện tại)
        if month < 12:
            next_month = datetime(current_year, month + 1, 1)
        else:
            next_month = datetime(current_year + 1, 1, 1)
        end_date = next_month - timedelta(days=1)

        # Tạo danh sách ngày trong tháng
        date_list = [(start_date + timedelta(days=i)).strftime("%Y-%m-%d")
                     for i in range((end_date - start_date).days + 1)]

        # Thêm vào dict với key "tháng X"
        month_key = f"tháng {month}"
        time_period[year_key][month_key] = date_list
    return time_period


def get_current_month_time_periods():
    time_period = {}
    current_date = datetime.now()
    period_key = current_date.strftime("%Y-%m")
    month_key = f"tháng {current_date.month}"
    start_date = current_date.replace(day=1)
    end_date = (start_date + timedelta(days=31)).replace(day=1) - timedelta(days=1)
    date_list = [(start_date + timedelta(days=i)).strftime("%Y-%m-%d")
                 for i in range((end_date - start_date).days + 1)]
    time_period[period_key] = {month_key: date_list}
    return time_period
