import json
import yaml
from typing import Dict, List
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_openapi_schema(openapi_data: Dict) -> bool:
    """Validate the OpenAPI schema for required fields."""
    required_fields = ["openapi", "info", "paths"]
    for field in required_fields:
        if field not in openapi_data:
            logger.error(f"OpenAPI schema validation failed: Missing '{field}' field.")
            return False
    if not openapi_data["paths"]:
        logger.error("OpenAPI schema validation failed: No paths defined.")
        return False
    return True

def generate_dynamic_example(parameters: Dict, required: List[str]) -> Dict:
    """Generate a dynamic example based on parameters and required fields."""
    example = {}
    for param_name, param_details in parameters.items():
        param_type = param_details.get("type")
        default = param_details.get("default")
        if param_name in required or default is not None:
            if param_type == "string":
                example[param_name] = default if default is not None else "example"
            elif param_type == "integer":
                example[param_name] = default if default is not None else 1
            elif param_type == "array":
                items_type = param_details.get("items", {}).get("type", "string")
                example[param_name] = default if default is not None else [items_type if items_type == "string" else 1]
            elif param_type == "object":
                example[param_name] = generate_dynamic_example(
                    param_details.get("properties", {}),
                    param_details.get("required", [])
                )
    return example

def parse_openapi_to_subdomains(openapi_file: str, domain: str) -> Dict[str, Dict]:
    """Parse OpenAPI YAML file and categorize APIs into sub-domains with enriched metadata."""
    with open(openapi_file, "r", encoding="utf-8") as f:
        openapi_data = yaml.safe_load(f)

    if not validate_openapi_schema(openapi_data):
        raise ValueError("Invalid OpenAPI schema.")

    paths = openapi_data.get("paths", {})
    subdomains = {
        "attendance": {
            "metadata": {"intent_keywords": ["late", "miss", "timesheet", "chấm công", "đi muộn", "phân ca"]},
            "apis": {}
        },
        "leave": {"metadata": {"intent_keywords": ["leave", "nghỉ phép", "balance", "số ngày nghỉ"]}, "apis": {}},
        "profile": {"metadata": {"intent_keywords": ["employee", "nhân viên", "list", "danh sách", "hồ sơ"]}, "apis": {}},
        "shift": {"metadata": {"intent_keywords": ["shift", "ca làm việc", "schedule", "lịch làm"]}, "apis": {}}
    }

    # Parse APIs into subdomains
    for path, methods in paths.items():
        for method, details in methods.items():
            api_name = f"{path.replace('/', '_')[1:]}_{method}"
            summary = details.get("summary", "No summary provided")
            description = details.get("description", "No description provided")
            request_body = details.get("requestBody", {}).get("content", {}).get("application/json", {})
            schema = request_body.get("schema", {}) if method.lower() == "post" else {
                param["name"]: param["schema"] for param in details.get("parameters", [])
            }
            example = request_body.get("example", {}) if method.lower() == "post" else details.get("example", {})

            # Determine subdomain
            subdomain = "attendance"  # Default
            if "Attendance" in summary or "checkin" in path:
                subdomain = "attendance"
            elif "Leave" in summary:
                subdomain = "leave"
            elif "Employee" in summary:
                subdomain = "profile"
            elif "Shift" in summary:
                subdomain = "shift"

            # Enrich parameter descriptions
            parameters = schema.get("properties", {}) if method.lower() == "post" else schema
            enriched_parameters = {}
            for param_name, param_details in parameters.items():
                param_description = param_details.get("description", f"Parameter {param_name}")
                enriched_param = {
                    "type": param_details.get("type"),
                    "default": param_details.get("default"),
                    "description": param_description,
                    "required": param_name in schema.get("required", []) if method.lower() == "post" else param_details.get("required", False)
                }
                # Handle nested objects (e.g., query.type.$in, query.datetime.$gte)
                if enriched_param["type"] == "object":
                    enriched_param["properties"] = param_details.get("properties", {})
                    enriched_param["required"] = param_details.get("required", [])
                # Handle arrays (e.g., sort, searchFields)
                if enriched_param["type"] == "array":
                    enriched_param["items"] = param_details.get("items", {}).get("type", "string")
                enriched_parameters[param_name] = enriched_param

            api_def = {
                "summary": summary,
                "description": description,
                "path": path,
                "method": method.upper(),
                "parameters": enriched_parameters,
                "required": schema.get("required", []) if method.lower() == "post" else [
                    p["name"] for p in details.get("parameters", []) if p.get("required", False)
                ],
                "example": example if example else generate_dynamic_example(
                    enriched_parameters,
                    schema.get("required", []) if method.lower() == "post" else [
                        p["name"] for p in details.get("parameters", []) if p.get("required", False)
                    ]
                ),
            }
            subdomains[subdomain]["apis"][api_name] = api_def

    return subdomains

def generate_domain_system_prompt(domain: str, subdomains: Dict[str, Dict], output_dir: str) -> None:
    """Generate a general system prompt for the domain."""
    subdomain_list = [f"{domain}/{sub}" for sub in subdomains.keys() if subdomains[sub]["apis"]]
    subdomain_apis = ", ".join([f"'{sub}/metadata.json' and '{sub}/apis/'" for sub in subdomain_list])

    prompt = f"""
    You are an intelligent assistant for an internal company chatbot, specializing in {domain} information. Your task is to:
    - Analyze the user's question and determine the intent.
    - Extract relevant entities (e.g., names, dates) from the question.
    - Return a response **strictly in JSON format** with "intent" and "entities" (and optionally "function" for API calls).
    - Do not include any additional text, explanations, or commentary outside the JSON unless the user explicitly asks for them.

    Possible intents:
    - "api_call": For questions requiring internal {domain} API calls (e.g., {', '.join(subdomains.keys())}).
    - "chat": For general conversation or unclear questions.

    Available APIs:
    - API metadata is stored in sub-domain folders: {subdomain_apis}.
    - Each sub-domain has a 'metadata.json' file with 'intent_keywords', and an 'apis/' folder with individual API definitions.
    - Use intent keywords from 'metadata.json' to identify the subdomain.
    - Match the question to an API from the 'apis/' folder based on summary, use_case, and example_queries.
    - Use the full 'parameters' from the API definition to construct the parameter set.

    Rules:
    - Always return valid JSON with an "intent" field and no additional text outside the JSON.
    - For "api_call":
      - Include "function" with "name", "path", "method", and "params".
      - Load the specific API definition from the 'apis/' folder after subdomain routing.
      - Fill required parameters using schema defaults if not provided in the query.
    - Extract entities relevant to {domain} (e.g., "name", "month", "year" from "March 2025" → month=2, year=2025).
    - Support both Vietnamese and English (e.g., "tháng 3" = "March").
    - If the question is ambiguous, return {{"intent": "chat", "text": "Please provide more details (e.g., time period or employee name)."}}
    - Use conversation history to refine intent if relevant.
    """
    print("System prompt: ", prompt)
    output_file = os.path.join(output_dir, domain, f"{domain}_system_prompt.json")
    prompt_data = {"system_prompt": prompt}
    try:
        os.makedirs(os.path.join(output_dir, domain), exist_ok=True)
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, ensure_ascii=False, indent=2)
        logger.info(f"Generated system prompt for {domain} saved to {output_file}")
    except Exception as e:
        logger.error(f"Failed to save system prompt for {domain}: {str(e)}")
        raise

def save_subdomain_api_metadata(domain: str, subdomain: str, subdomain_data: Dict, output_dir: str) -> None:
    """Save subdomain metadata and individual APIs as OpenAI function schemas with all parameters."""
    base_path = os.path.join(output_dir, domain, subdomain)
    os.makedirs(base_path, exist_ok=True)

    # Save subdomain metadata (no common_parameters)
    metadata_file = os.path.join(base_path, "metadata.json")
    try:
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(subdomain_data["metadata"], f, ensure_ascii=False, indent=2)
        logger.info(f"Generated metadata for {domain}/{subdomain} saved to {metadata_file}")
    except Exception as e:
        logger.error(f"Failed to save metadata for {domain}/{subdomain}: {str(e)}")
        raise

    # Save individual APIs as OpenAI function schemas
    apis_path = os.path.join(base_path, "apis")
    os.makedirs(apis_path, exist_ok=True)
    for api_name, api_def in subdomain_data["apis"].items():
        api_file = os.path.join(apis_path, f"{api_name}.json")

        # Construct OpenAI function schema
        parameters = {
            "type": "object",
            "properties": {},
            "required": api_def["required"],
            "additionalProperties": False
        }
        # Use full parameters directly
        for param_name, param_details in api_def["parameters"].items():
            param_schema = {
                "type": param_details["type"],
                "description": param_details["description"]
            }
            if "default" in param_details and param_details["default"] is not None:
                param_schema["default"] = param_details["default"]
            if param_details["type"] == "array":
                param_schema["items"] = {"type": param_details.get("items", "string")}
            elif param_details["type"] == "object":
                param_schema["properties"] = {
                    sub_param: {
                        "type": sub_details.get("type", "string"),
                        "description": f"Sub-parameter of {param_name}"
                    } for sub_param, sub_details in param_details.get("properties", {}).items()
                }
                param_schema["required"] = param_details.get("required", [])
                # Handle nested arrays or objects (e.g., type.$in)
                for sub_param, sub_details in param_details.get("properties", {}).items():
                    if sub_details.get("type") == "array":
                        param_schema["properties"][sub_param]["items"] = {"type": sub_details.get("items", {}).get("type", "string")}
                    elif sub_details.get("type") == "object":
                        param_schema["properties"][sub_param]["properties"] = sub_details.get("properties", {})
                        param_schema["properties"][sub_param]["required"] = sub_details.get("required", [])
            parameters["properties"][param_name] = param_schema
        # Add extra path and method for call api
        # parameters["properties"]["path"] = {
        #     "type": "string",
        #     "description": "Parameter path",
        #     "default": api_def["path"]
        # }
        # parameters["properties"]["method"] = {
        #     "type": "string",
        #     "description": "Parameter method",
        #     "method": api_def["method"]
        # }
        # parameters["required"].extend(["path", "method"])
        # api_def["example"]["path"] = api_def["path"]
        # api_def["example"]["method"] = api_def["method"]

        function_schema = {
            "type": "function",
            "function": {
                "name": api_name,
                "description": f"{api_def['summary']} - {api_def['description']}",
                "parameters": parameters,
                "path": api_def["path"],
                "method": api_def["method"],
                "example": api_def["example"]  # Include the example directly from OpenAPI
            }
        }

        try:
            with open(api_file, "w", encoding="utf-8") as f:
                json.dump(function_schema, f, ensure_ascii=False, indent=2)
            logger.info(f"Generated API function schema for {domain}/{subdomain}/{api_name} saved to {api_file}")
        except Exception as e:
            logger.error(f"Failed to save API metadata for {domain}/{subdomain}/{api_name}: {str(e)}")
            raise

if __name__ == "__main__":
    domain = "hr"
    openapi_file = "/home/<USER>/Documents/AI/HRBL_AI/chatbot-rbl/src/api_defintions/hr/attendance_apis.yaml"
    output_dir = "/home/<USER>/Documents/AI/HRBL_AI/chatbot-rbl/src/domains"

    subdomains = parse_openapi_to_subdomains(openapi_file, domain)
    generate_domain_system_prompt(domain, subdomains, output_dir)
    for subdomain, subdomain_data in subdomains.items():
        if subdomain_data["apis"]:
            save_subdomain_api_metadata(domain, subdomain, subdomain_data, output_dir)