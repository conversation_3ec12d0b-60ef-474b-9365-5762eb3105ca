import re
import calendar

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import Optional

import uvicorn

from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

app = FastAPI(
    title="Time Extraction API",
    description="API endpoint to extract time range from Vietnamese text expressions",
    version="1.0.0"
)


class TimeExtractionRequest(BaseModel):
    text: str
    reference_date: Optional[str] = None


class TimeExtractionResponse(BaseModel):
    start_date: str
    end_date: str
    reference_date: str


def get_week_range(date):
    """Get the date range for the week containing the specified date."""
    start_date = date - timedelta(days=date.weekday())
    end_date = start_date + timedelta(days=6)
    return start_date, end_date


def get_month_range(date):
    """Get the date range for the month containing the specified date."""
    start_date = date.replace(day=1)
    # Calculate the last day of the month correctly
    last_day = calendar.monthrange(date.year, date.month)[1]
    end_date = date.replace(day=last_day)
    return start_date, end_date


def get_year_range(date):
    """Get the date range for the year containing the specified date."""
    start_date = date.replace(month=1, day=1)
    end_date = date.replace(month=12, day=31)
    return start_date, end_date


def get_quarter_range(date):
    """Get the date range for the quarter containing the specified date."""
    quarter = (date.month - 1) // 3 + 1
    start_month = (quarter - 1) * 3 + 1

    # Set the start date to the first day of the first month of the quarter
    start_date = date.replace(month=start_month, day=1)

    # Set the end date to the last day of the last month of the quarter
    if quarter < 4:  # Q1, Q2, Q3
        end_month = start_month + 2
        end_date = date.replace(month=end_month)
        last_day = calendar.monthrange(date.year, end_month)[1]
        end_date = end_date.replace(day=last_day)
    else:  # Q4
        end_date = date.replace(month=12, day=31)

    return start_date, end_date


def safe_month_adjust(date, month_delta):
    """Safely adjust a date's month by the given delta, handling year boundaries."""
    target_year = date.year + ((date.month + month_delta - 1) // 12)
    target_month = ((date.month + month_delta - 1) % 12) + 1

    # Make sure we don't exceed the number of days in the target month
    max_day = calendar.monthrange(target_year, target_month)[1]
    target_day = min(date.day, max_day)

    return date.replace(year=target_year, month=target_month, day=target_day)


def parse_time_expression(text, reference_date):
    """Parse Vietnamese time expressions and return a date range."""
    text = text.lower().strip()
    today = reference_date

    # Dictionary of Vietnamese weekday names to weekday indices (0=Monday, 6=Sunday)
    weekdays_vi = {
        "thứ hai": 0, "thứ ba": 1, "thứ tư": 2, "thứ năm": 3, "thứ sáu": 4, "thứ bảy": 5, "chủ nhật": 6,
        "thứ 2": 0, "thứ 3": 1, "thứ 4": 2, "thứ 5": 3, "thứ 6": 4, "thứ 7": 5
    }

    # Month names in Vietnamese
    months_vi = {
        "tháng một": 1, "tháng 1": 1, "tháng giêng": 1,
        "tháng hai": 2, "tháng 2": 2,
        "tháng ba": 3, "tháng 3": 3,
        "tháng tư": 4, "tháng 4": 4,
        "tháng năm": 5, "tháng 5": 5,
        "tháng sáu": 6, "tháng 6": 6,
        "tháng bảy": 7, "tháng 7": 7,
        "tháng tám": 8, "tháng 8": 8,
        "tháng chín": 9, "tháng 9": 9,
        "tháng mười": 10, "tháng 10": 10,
        "tháng mười một": 11, "tháng 11": 11,
        "tháng mười hai": 12, "tháng 12": 12,
        "tháng chạp": 12
    }

    # Handle today, yesterday, tomorrow
    if text in ["hôm nay", "ngày hôm nay"]:
        return today, today

    if text in ["hôm qua", "ngày hôm qua"]:
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday

    if text in ["ngày mai", "hôm sau"]:
        tomorrow = today + timedelta(days=1)
        return tomorrow, tomorrow

    if text in ["ngày kia", "ngày mốt"]:
        day_after_tomorrow = today + timedelta(days=2)
        return day_after_tomorrow, day_after_tomorrow

    if text in ["hôm kia"]:
        day_before_yesterday = today - timedelta(days=2)
        return day_before_yesterday, day_before_yesterday

    # Match specific date formats: "ngày DD/MM/YYYY", "DD/MM/YYYY", "DD-MM-YYYY"
    if match := re.search(r"(?:ngày\s+)?(\d{1,2})[/-](\d{1,2})(?:[/-](\d{2,4}))?", text):
        day = int(match.group(1))
        month = int(match.group(2))

        if match.group(3):  # Year is specified
            year = int(match.group(3))
            if year < 100:  # Convert 2-digit year to 4-digit
                current_century = (today.year // 100) * 100
                year = current_century + year
        else:
            year = today.year

        # Validate date
        try:
            target_date = datetime(year, month, day).date()
            return target_date, target_date
        except ValueError:
            # If invalid date, fallback to today
            return today, today

    # Match "ngày DD [tháng MM] [năm YYYY]"
    if match := re.search(r"ngày\s+(\d{1,2})(?:\s+tháng\s+(\d{1,2}))?(?:\s+năm\s+(\d{4}))?", text):
        day = int(match.group(1))
        month = int(match.group(2)) if match.group(2) else today.month
        year = int(match.group(3)) if match.group(3) else today.year

        try:
            target_date = datetime(year, month, day).date()
            return target_date, target_date
        except ValueError:
            # If invalid date, fallback to today
            return today, today

    # Match day of current month: "ngày 15", "ngày 15 tháng này"
    if match := re.search(r"ngày\s+(\d{1,2})(?:\s+tháng\s+này)?", text):
        day = int(match.group(1))
        try:
            target_date = today.replace(day=day)
            return target_date, target_date
        except ValueError:
            # If day is invalid for the month, use the last day of the month
            last_day = calendar.monthrange(today.year, today.month)[1]
            target_date = today.replace(day=last_day)
            return target_date, target_date

    # Match specific weekday with optional modifiers
    if match := re.search(
            r"(thứ (?:hai|ba|tư|năm|sáu|bảy|\d+)|chủ nhật)(?:\s+(tuần trước|tuần sau|tuần trước nữa|tuần tới))?", text):
        day_str = match.group(1)
        modifier = match.group(2) or ""

        if day_str in weekdays_vi:
            day_index = weekdays_vi[day_str]
            current_weekday = today.weekday()

            if "tuần trước nữa" in modifier:
                # Two weeks ago
                # Go back 14 days to get to two weeks ago
                two_weeks_ago = today - timedelta(days=14)
                # Calculate how many days to add to get to the target weekday
                days_to_add = (day_index - two_weeks_ago.weekday()) % 7
                target_date = two_weeks_ago + timedelta(days=days_to_add)
            elif "tuần trước" in modifier:
                # Last week
                # Go back 7 days to get to last week
                last_week = today - timedelta(days=7)
                # Calculate how many days to add to get to the target weekday
                days_to_add = (day_index - last_week.weekday()) % 7
                target_date = last_week + timedelta(days=days_to_add)
            elif "tuần sau" in modifier or "tuần tới" in modifier:
                # Next week
                # Go forward 7 days to get to next week
                next_week = today + timedelta(days=7)
                # Calculate how many days to add to get to the target weekday
                days_to_add = (day_index - next_week.weekday()) % 7
                target_date = next_week + timedelta(days=days_to_add)
            else:
                # This week
                # Calculate days until the next occurrence of the target weekday
                days_diff = (day_index - current_weekday) % 7
                if days_diff == 0:
                    # Same day of the week, use today
                    target_date = today
                elif days_diff > 0:
                    # Later this week
                    target_date = today + timedelta(days=days_diff)
                else:
                    # Earlier this week (already passed), go to last week
                    target_date = today - timedelta(days=7 - abs(days_diff))

            return target_date, target_date

    # Handle time periods
    if "tuần này" in text:
        return get_week_range(today)

    if "tháng này" in text:
        return get_month_range(today)

    if "năm nay" in text:
        return get_year_range(today)

    if "quý này" in text:
        return get_quarter_range(today)

    # Handle relative past periods
    if "tuần trước" in text and not any(day in text for day in weekdays_vi):
        if match := re.search(r"(\d+)\s+tuần trước", text):
            # "X weeks ago"
            weeks = int(match.group(1))
            past_date = today - timedelta(days=7 * weeks)
            return get_week_range(past_date)
        else:
            # "Last week"
            prev_week = today - timedelta(days=7)
            return get_week_range(prev_week)

    if "tháng trước" in text:
        if match := re.search(r"(\d+)\s+tháng trước", text):
            # "X months ago"
            months = int(match.group(1))
            past_date = safe_month_adjust(today, -months)
            return get_month_range(past_date)
        else:
            # "Last month"
            prev_month = safe_month_adjust(today, -1)
            return get_month_range(prev_month)

    if "năm trước" in text:
        if match := re.search(r"(\d+)\s+năm trước", text):
            # "X years ago"
            years = int(match.group(1))
            past_date = today.replace(year=today.year - years)
            return get_year_range(past_date)
        else:
            # "Last year"
            prev_year = today.replace(year=today.year - 1)
            return get_year_range(prev_year)

    if "quý trước" in text:
        # Get the correct previous quarter, handling year boundaries
        current_quarter = (today.month - 1) // 3 + 1
        if current_quarter == 1:
            # If Q1, previous quarter is Q4 of previous year
            prev_quarter_date = today.replace(year=today.year - 1, month=10, day=1)
        else:
            # Otherwise, go back 3 months
            prev_quarter_date = safe_month_adjust(today, -3)

        return get_quarter_range(prev_quarter_date)

    # Handle relative future periods
    if "tuần sau" in text or "tuần tới" in text:
        if match := re.search(r"(\d+)\s+tuần (?:sau|tới)", text):
            # "X weeks from now"
            weeks = int(match.group(1))
            future_date = today + timedelta(days=7 * weeks)
            return get_week_range(future_date)
        else:
            # "Next week"
            next_week = today + timedelta(days=7)
            return get_week_range(next_week)

    if "tháng sau" in text or "tháng tới" in text:
        if match := re.search(r"(\d+)\s+tháng (?:sau|tới)", text):
            # "X months from now"
            months = int(match.group(1))
            future_date = safe_month_adjust(today, months)
            return get_month_range(future_date)
        else:
            # "Next month"
            next_month = safe_month_adjust(today, 1)
            return get_month_range(next_month)

    if "năm sau" in text or "năm tới" in text:
        if match := re.search(r"(\d+)\s+năm (?:sau|tới)", text):
            # "X years from now"
            years = int(match.group(1))
            future_date = today.replace(year=today.year + years)
            return get_year_range(future_date)
        else:
            # "Next year"
            next_year = today.replace(year=today.year + 1)
            return get_year_range(next_year)

    if "quý sau" in text or "quý tới" in text:
        # Get the correct next quarter, handling year boundaries
        current_quarter = (today.month - 1) // 3 + 1
        if current_quarter == 4:
            # If Q4, next quarter is Q1 of next year
            next_quarter_date = today.replace(year=today.year + 1, month=1, day=1)
        else:
            # Otherwise, go forward 3 months
            next_quarter_date = safe_month_adjust(today, 3)

        return get_quarter_range(next_quarter_date)

    # Handle half-year periods
    if "nửa năm đầu" in text or "nửa đầu năm" in text:
        year = today.year
        if "năm trước" in text:
            year -= 1
        elif "năm sau" in text or "năm tới" in text:
            year += 1

        start_date = datetime(year, 1, 1).date()
        end_date = datetime(year, 6, 30).date()
        return start_date, end_date

    if "nửa năm cuối" in text or "nửa cuối năm" in text:
        year = today.year
        if "năm trước" in text:
            year -= 1
        elif "năm sau" in text or "năm tới" in text:
            year += 1

        start_date = datetime(year, 7, 1).date()
        end_date = datetime(year, 12, 31).date()
        return start_date, end_date

    # Handle beginning and end of periods
    if "đầu tuần" in text:
        if "tuần trước" in text:
            reference = today - timedelta(days=7)
        elif "tuần sau" in text or "tuần tới" in text:
            reference = today + timedelta(days=7)
        else:
            reference = today

        start_date, _ = get_week_range(reference)
        end_date = start_date + timedelta(days=2)  # Monday-Wednesday
        return start_date, end_date

    if "cuối tuần" in text:
        if "tuần trước" in text:
            reference = today - timedelta(days=7)
        elif "tuần sau" in text or "tuần tới" in text:
            reference = today + timedelta(days=7)
        else:
            reference = today

        start_date, end_date = get_week_range(reference)
        start_date = end_date - timedelta(days=2)  # Friday-Sunday
        return start_date, end_date

    if "đầu tháng" in text:
        if "tháng trước" in text:
            reference = safe_month_adjust(today, -1)
        elif "tháng sau" in text or "tháng tới" in text:
            reference = safe_month_adjust(today, 1)
        else:
            reference = today

        start_date, _ = get_month_range(reference)
        end_date = start_date + timedelta(days=9)  # First 10 days
        return start_date, end_date

    if "cuối tháng" in text:
        if "tháng trước" in text:
            reference = safe_month_adjust(today, -1)
        elif "tháng sau" in text or "tháng tới" in text:
            reference = safe_month_adjust(today, 1)
        else:
            reference = today

        _, end_date = get_month_range(reference)
        start_date = end_date - timedelta(days=9)  # Last 10 days
        return start_date, end_date

    # Handle specific month expressions
    if match := re.search(r"tháng (\d{1,2}|\w+)(?:\s+năm (\d{4}))?", text):
        month_value = match.group(1)
        year = int(match.group(2)) if match.group(2) else today.year

        # Try to convert month name or number
        try:
            if month_value.isdigit():
                month = int(month_value)
            else:
                month_key = f"tháng {month_value}"
                if month_key in months_vi:
                    month = months_vi[month_key]
                else:
                    month = today.month

            if 1 <= month <= 12:
                target_date = datetime(year, month, 1).date()
                return get_month_range(target_date)
        except (ValueError, KeyError):
            pass

    # Handle specific quarter expressions
    if match := re.search(r"quý (\d)(?:\s+năm (\d{4}))?", text):
        quarter = int(match.group(1))
        year = int(match.group(2)) if match.group(2) else today.year

        if 1 <= quarter <= 4:
            month = (quarter - 1) * 3 + 1
            target_date = datetime(year, month, 1).date()
            return get_quarter_range(target_date)

    # Handle "tuần đầu tháng X" or "tuần cuối tháng X"
    if match := re.search(r"tuần (đầu|cuối) tháng (\d{1,2}|\w+)(?:\s+năm (\d{4}))?", text):
        position = match.group(1)  # "đầu" or "cuối"
        month_value = match.group(2)
        year = int(match.group(3)) if match.group(3) else today.year

        # Try to convert month name or number
        try:
            if month_value.isdigit():
                month = int(month_value)
            else:
                month_key = f"tháng {month_value}"
                if month_key in months_vi:
                    month = months_vi[month_key]
                else:
                    month = today.month

            if 1 <= month <= 12:
                # Find the first day of the month
                first_day = datetime(year, month, 1).date()
                last_day = first_day.replace(day=calendar.monthrange(year, month)[1])

                if position == "đầu":
                    # First week of the month
                    end_day = min(first_day + timedelta(days=6), last_day)
                    return first_day, end_day
                else:  # "cuối"
                    # Last week of the month
                    # Find the last Sunday of the month
                    last_sunday = last_day - timedelta(days=(last_day.weekday() + 1) % 7)
                    # Go back to the beginning of that week (Monday)
                    start_day = last_sunday - timedelta(days=6)
                    if start_day.month != month:
                        start_day = first_day
                    return start_day, last_day
        except (ValueError, KeyError):
            pass

    # Default fallback - return the current month
    return get_month_range(today)


@app.post("/extract-time", response_model=TimeExtractionResponse)
async def extract_time(request: TimeExtractionRequest):
    """API endpoint to extract time range from Vietnamese text expressions."""
    try:
        text = request.text
        reference_date_str = request.reference_date

        if not text:
            raise HTTPException(status_code=400, detail="No text provided")

        # Parse reference date or use current date
        if reference_date_str:
            try:
                reference_date = datetime.strptime(reference_date_str, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid reference_date format. Use YYYY-MM-DD")
        else:
            reference_date = datetime.now().date()

        start_date, end_date = parse_time_expression(text, reference_date)

        return TimeExtractionResponse(
            start_date=start_date.strftime("%Y-%m-%d"),
            end_date=end_date.strftime("%Y-%m-%d"),
            reference_date=reference_date.strftime("%Y-%m-%d")
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def calculate_years_worked(start_date_str: str, date_format: str = "%Y-%m-%d") -> str:
    """
    Calculate the difference between the given date and today,
    and return the result in years, months, and days.

    Args:
        start_date_str (str): The start date as a string (e.g., "2020-03-15").
        date_format (str): The format of the input date string (default: "%Y-%m-%d").

    Returns:
        str: A string in the format "X years Y months Z days".
    """
    # Convert the input string to a datetime object
    start_date = datetime.strptime(start_date_str, date_format)
    current_date = datetime.now()

    # Calculate the difference using relativedelta
    delta = relativedelta(current_date, start_date)
    if delta.years != 0:
        if delta.months != 0:
            return f"{delta.years} năm {delta.months} tháng"
        return f"{delta.years} năm"
    elif delta.months != 0:
        if delta.days != 0:
            return f"{delta.months} tháng {delta.days} ngày"
        return f"{delta.months} tháng"
    return f"{delta.days} ngày"


if __name__ == "__main__":
    # current_date = datetime.now().strftime("%Y-%m-%d")
    # reference_date = datetime.strptime(current_date, "%Y-%m-%d").date()
    # start_date, end_date = parse_time_expression(
    #     "Thứ năm tuần trước Trần Bá Cường có đi muộn không và chấm công ở đâu?", reference_date)
    uvicorn.run(app, host="0.0.0.0", port=5000)
