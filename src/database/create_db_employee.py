import logging
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
import requests

load_dotenv()

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

DB_FILE = "hr_management.db"

API_URL = "https://api.weekly.vn/employee/list"
API_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {os.getenv('API_TOKEN')}"
}


def calculate_years_worked(start_date):
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        current = datetime.now()
        days = (current - start).days
        return days // 365
    except (ValueError, TypeError):
        logging.warning(f"Invalid start_date: {start_date}, defaulting to 0 years")
        return 0


def init_db():
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Check if old 'id' column exists
    cursor.execute("PRAGMA table_info(employees)")
    columns = [col[1] for col in cursor.fetchall()]
    if "id" in columns and "employee_id" not in columns:
        cursor.execute("ALTER TABLE employees RENAME COLUMN id TO employee_id")

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS employees (
            employee_id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            hr_code TEXT NOT NULL,
            start_date TEXT NOT NULL,
            position TEXT NOT NULL,
            department TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'working',
            employment_type TEXT NOT NULL,
            years_worked INTEGER NOT NULL DEFAULT 0
        )
    """)

    cursor.execute("CREATE INDEX IF NOT EXISTS idx_name ON employees(name)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_position ON employees(position)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_department ON employees(department)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_employment_type ON employees(employment_type)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_years_worked ON employees(years_worked)")

    conn.commit()
    conn.close()
    logging.info("Database initialized")


def connect_db():
    return sqlite3.connect(DB_FILE)


def fetch_employees_from_api():
    payload = {
        "query": {"status": "working"},
        "search": "",
        "page": 1,
        "total": 0,
        "totalPages": 0,
        "pageSize": 200,
        "searchFields": ["hr_code", "name", "email"],
        "sort": ["hr_code_sort"],
        "status": "working"
    }

    try:
        response = requests.post(API_URL, json=payload, headers=API_HEADERS)
        response.raise_for_status()
        data = response.json()
        employees = data.get("data", [])
        logging.info(f"Fetched {len(employees)} employees from API")
        return employees
    except requests.exceptions.RequestException as e:
        logging.error(f"Error calling API: {e}")
        return []


def save_or_update_employees(employees):
    conn = connect_db()
    cursor = conn.cursor()

    try:
        for emp in employees:
            employment_type = ""
            if isinstance(emp.get("employment_type"), dict):
                employment_type = emp.get("employment_type", {}).get("name", "")
            elif emp.get("employment_type") is not None:
                employment_type = str(emp.get("employment_type"))

            position = ""
            department = ""
            positions = emp.get("positions", [])
            if positions and isinstance(positions, list) and len(positions) > 0:
                position = positions[0].get("position", {}).get("name", "") if isinstance(positions[0].get("position"),
                                                                                          dict) else str(
                    positions[0].get("position", ""))
                department = positions[0].get("department", {}).get("title", "") if isinstance(
                    positions[0].get("department"), dict) else str(positions[0].get("department", ""))

            employee_data = {
                "employee_id": emp.get("id"),
                "name": emp.get("name", ""),
                "email": emp.get("email", ""),
                "hr_code": emp.get("hr_code", ""),
                "start_date": emp.get("start_date", "1970-01-01"),
                "position": position,
                "department": department,
                "status": emp.get("status", "working"),
                "employment_type": employment_type,
                "years_worked": calculate_years_worked(emp.get("start_date", "1970-01-01"))
            }

            if not employee_data["email"]:
                logging.warning(f"Skipping employee with missing email: {employee_data}")
                continue

            # Fix: Change 'id' to 'employee_id' in the SELECT query
            cursor.execute("SELECT employee_id FROM employees WHERE email = ?", (employee_data["email"],))
            existing = cursor.fetchone()

            if existing:
                cursor.execute("""
                    UPDATE employees 
                    SET employee_id = ?, name = ?, hr_code = ?, start_date = ?, position = ?, 
                        department = ?, status = ?, employment_type = ?, years_worked = ?
                    WHERE email = ?
                """, (
                    employee_data["employee_id"], employee_data["name"], employee_data["hr_code"],
                    employee_data["start_date"], employee_data["position"], employee_data["department"],
                    employee_data["status"], employee_data["employment_type"], employee_data["years_worked"],
                    employee_data["email"]
                ))
            else:
                cursor.execute("""
                    INSERT INTO employees (
                        employee_id, name, email, hr_code, start_date, position, department, 
                        status, employment_type, years_worked
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    employee_data["employee_id"], employee_data["name"], employee_data["email"],
                    employee_data["hr_code"], employee_data["start_date"], employee_data["position"],
                    employee_data["department"], employee_data["status"], employee_data["employment_type"],
                    employee_data["years_worked"]
                ))

        conn.commit()
        logging.info("Employees saved/updated successfully")
    except sqlite3.Error as e:
        logging.error(f"Error saving employees: {e}")
    finally:
        conn.close()


def update_database():
    logging.info("Updating database from API...")
    employees = fetch_employees_from_api()
    if employees:
        save_or_update_employees(employees)


def query_by_name(name):
    conn = connect_db()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT * FROM employees WHERE name LIKE ?", (f"%{name}%",))
        results = [dict(row) for row in cursor.fetchall()]
        return results
    finally:
        conn.close()


def query_by_position_department(position=None, department=None):
    conn = connect_db()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    query = "SELECT * FROM employees WHERE 1=1"
    params = []

    if position:
        query += " AND position = ?"
        params.append(position)
    if department:
        query += " AND department = ?"
        params.append(department)

    try:
        cursor.execute(query, params)
        results = [dict(row) for row in cursor.fetchall()]
        return results
    finally:
        conn.close()


def query_by_employment_type_and_duration(employment_type=None, min_years=None):
    conn = connect_db()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    query = "SELECT * FROM employees WHERE 1=1"
    params = []

    if employment_type:
        query += " AND employment_type = ?"
        params.append(employment_type)
    if min_years is not None:
        query += " AND years_worked >= ?"
        params.append(min_years)

    try:
        cursor.execute(query, params)
        results = [dict(row) for row in cursor.fetchall()]
        return results
    finally:
        conn.close()


# Hàm chính
def main():
    init_db()
    update_database()

    print("Query by name 'Phong':")
    results = query_by_name("Dương")
    for emp in results:
        print(emp)

    print("\nQuery by position 'Nhân viên' and department 'D2':")
    results = query_by_position_department(position="Phó Giám đốc", department="BOD")
    for emp in results:
        print(emp)

    print("\nQuery by employment_type 'Full-time Permanent' and min 2 years worked:")
    results = query_by_employment_type_and_duration(employment_type="Fulltime chính thức", min_years=2)
    for emp in results:
        print(emp)


if __name__ == "__main__":
    main()
