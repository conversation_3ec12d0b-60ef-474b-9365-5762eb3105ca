import os
import json
import logging

from typing import Dict, List, Optional
from langfuse.decorators import langfuse_context, observe
from dotenv import load_dotenv
from litellm import acompletion
import litellm

load_dotenv()
logger = logging.getLogger(__name__)

# Configurable defaults
TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", 0.0))
MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", 4096))
litellm.success_callback = ["langfuse"]
litellm.failure_callback = ["langfuse"]


class LLMProvider:
    def __init__(self, provider: str = "openai"):
        """
        Initialize the LLM provider with a specific provider type.
        :param provider: "openai", "openrouter", or "vllm"
        """
        self.provider_configs = {
            "openai": {
                "api_key": os.getenv("OPENAI_API_KEY"),
                "model": os.getenv("OPENAI_MODEL", "openai/gpt-4o-mini"),
                "api_base": None,
                "validation": lambda c: c["api_key"] or "OPENAI_API_KEY is required for openai"
            },
            "openrouter": {
                "api_key": os.getenv("OPENROUTER_API_KEY"),
                "model": os.getenv("OPENROUTER_MODEL_NAME", "openrouter/meta-llama/llama-3.1-8b-instruct:free"),
                "api_base": None,
                "validation": lambda c: c["api_key"] or "OPENROUTER_API_KEY is required for openrouter"
            },
            "groq": {
                "api_key": os.getenv("GROQ_API_KEY"),
                "model": os.getenv("GROQ_MODEL_NAME", "groq/llama-3.3-70b-versatile"),
                "api_base": None,
                "validation": lambda c: c["api_key"] or "GROQ_API_KEY is required for groq"
            },
            "gemini": {
                "api_key": os.getenv("GEMINI_API_KEY"),
                "model": os.getenv("GEMINI_MODEL_NAME", "gemini/gemini-2.0-flash-lite"),
                "api_base": None,
                "validation": lambda c: c["api_key"] or "GEMINI_API_KEY is required for gemini"
            },
            "vllm": {
                "api_key": os.getenv("VLLM_API_KEY", "EMPTY"),
                "model": os.getenv("VLLM_MODEL_NAME"),
                "api_base": os.getenv("VLLM_API_BASE"),
                "validation": lambda c: (c["api_base"] and c[
                    "model"]) or "VLLM_API_BASE and MODEL_NAME are required for vllm"
            }
        }
        if isinstance(provider, str):
            if provider.lower() not in self.provider_configs:
                raise ValueError(f"Unsupported provider: {provider}")
            self.provider = {"paid": provider.lower(), "free": provider.lower()}
        elif isinstance(provider, dict) and "paid" in provider and "free" in provider:
            paid_provider = provider["paid"].lower()
            free_provider = provider["free"].lower()
            if paid_provider not in self.provider_configs:
                raise ValueError(f"Unsupported paid provider: {paid_provider}")
            if free_provider not in self.provider_configs:
                raise ValueError(f"Unsupported free provider: {free_provider}")
            self.provider = {"paid": paid_provider, "free": free_provider}
        else:
            raise ValueError("Provider must be a string or a dict with 'paid' and 'free' keys")
        self.paid_config = self.provider_configs[self.provider["paid"]]
        self.free_config = self.provider_configs[self.provider["free"]]

    # @observe()
    def _construct_messages_with_history(self, system_prompt: str, message: str, context: Dict,
                                         extra_instructions: str = "") -> List[Dict]:
        """Construct a list of messages including system prompt, history, and user message."""
        history = context.get("chat_history", [])
        messages = [{"role": "system", "content": system_prompt}]
        if history:
            messages.extend(history if len(history) <= 3 else history[:-3])

        user_content = f"{message}\n\n{extra_instructions}" if extra_instructions else message
        messages.append({"role": "user", "content": user_content})

        return messages

    # @observe(name="call_llm", as_type="generation")
    async def call_api(self, messages: List[Dict], use_paid: bool = False, **kwargs) -> str:
        """Call the LLM API and return the response content."""
        config = self.paid_config if use_paid else self.free_config
        provider_name = self.provider["paid"] if use_paid else self.provider["free"]
        try:
            response = await acompletion(
                model=config["model"],
                messages=messages,
                api_key=config["api_key"],
                api_base=config["api_base"],
                temperature=TEMPERATURE,
                max_tokens=MAX_TOKENS,
                metadata={
                    "generation_name": "call_llm",
                    "existing_trace_id": langfuse_context.get_current_trace_id(),  # set langfuse trace ID
                    "parent_observation_id": langfuse_context.get_current_observation_id()
                },
                **kwargs
            )
            content = response.choices[0].message.content.strip()
            logger.info(f"{provider_name} response: {content}")
            return content
        except Exception as e:
            logger.error(f"{provider_name} API call error: {str(e)}")
            raise

    async def call_api_with_tools(self, messages: List[Dict], tools: List[Dict], **kwargs) -> Dict:
        """Call the LLM API with tools and return the response message."""
        config = self.paid_config
        provider_name = self.provider["paid"]
        try:
            response = await acompletion(
                model=config["model"],
                messages=messages,
                tools=tools,
                tool_choice="auto",
                api_key=config["api_key"],
                api_base=config["api_base"],
                temperature=TEMPERATURE,
                max_tokens=MAX_TOKENS,
                **kwargs
            )
            response_message = response.choices[0].message
            logger.info(f"{self.provider} raw response message: {response_message}")
            return {
                "content": response_message.content or "",
                "tool_calls": [
                    {
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": json.loads(tool_call.function.arguments)
                        }
                    } for tool_call in (response_message.tool_calls or [])
                ]
            }
        except Exception as e:
            logger.error(f"{provider_name} tool calling error: {str(e)}")
            return {"content": "", "tool_calls": []}

    # @observe()
    async def process_message(self, message: str, context: Dict, system_prompt: str, **kwargs) -> str:
        """Analyze user message and generate a response."""
        try:
            messages = self._construct_messages_with_history(system_prompt, message, context)
            return await self.call_api(messages, **kwargs)
        except Exception as e:
            logger.error(f"{self.provider} processing error: {str(e)}")
            return "Tôi không hiểu rõ câu hỏi. Vui lòng cung cấp thông tin chi tiết hơn"

    async def process_message_with_tools(self, message: str, context: Dict, system_prompt: str, tools: List[Dict],
                                         **kwargs) -> Dict:
        """Process message with tool calling."""
        try:
            context["tools"] = tools
            messages = self._construct_messages_with_history(system_prompt, message, context)
            return await self.call_api_with_tools(messages, tools, **kwargs)
        except Exception as e:
            logger.error(f"{self.provider} tool processing error: {str(e)}")
            return {"content": "", "tool_calls": []}
