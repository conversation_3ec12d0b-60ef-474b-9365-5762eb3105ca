from src.agents.agent_base import BaseAgent

class ToolCallAgent(BaseAgent):
    def __init__(
        self,
        messages,
        llm_config,
        tool_dict,
        tools,
        agent_tools: list,
        tool_excutor
    ):
        super().__init__(llm_config)
        self.messages = messages
        self.tool_dict = tool_dict
        self.tools = tools
        self.agent_tools = agent_tools
        self.tool_excutor = tool_excutor

    async def on_message(self, message, **kwargs):
        self.messages.append({"role": "user", "content": message})
        return self.call_llm(**kwargs)

    async def call_tool(self, tool_call):
        function_response = await self.tool_excutor(tool_call)
        self.messages.append(
            {
                "role": "user",
                "name": tool_call.function.name,
                "content": function_response,
            }
        )

    async def call_llm(self, messages, **kwargs):
        if self.tools:
            kwargs['"tools'] = self.tools

        model_respone = self.acompletion_tools(messages, self.tools)






