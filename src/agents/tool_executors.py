import asyncio
import json
import os
from abc import ABC, abstractmethod

import httpx
from dotenv import load_dotenv

load_dotenv()

class ToolExecutor(ABC):
    @abstractmethod
    async def aexcute(self, tool_call, tool_dict):
        pass

class DefaultToolExecutor(ToolExecutor):
    async def aexcute(self, tool_call, tool_dict):
        return await excute_tool(tool_call, tool_dict)


async def excute_tool(
    tool_call,
    tool_dict,
    **extra_tool_args
):
    tool_name = tool_call.function.name
    if tool_name not in tool_dict:
        raise ValueError(f'{tool_name} is not valid tool name.')

    try:
        if tool_call.function.arguments:
            tool_args = json.load(tool_call.function.arguments)
        else:
            tool_args = {}
    except json.JSONDecodeError as e:
        raise ValueError(f'Tool call arguments were provide in invalid JSON format: {e}')

    func_result = excute_api(tool_args)
    if asyncio.iscoroutine(func_result):
        return await func_result
    else:
        return func_result

async def excute_api(
    tool_args
):
    path = tool_args.get("path")
    method = tool_args.get("method", "post").lower()
    params = {key: value for key, value in tool_args.items() if key not in ["path", "method"]}
    # Specific for weekly api
    if "month" in params:
        params['month'] = params['month'] - 1

    if not path or not method:
        raise ValueError("Function call must have 'path' and 'method'.")

    url = f"{os.getenv('API_BASE_URL')}{path}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('API_TOKEN')}"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.request(
                method=method,
                url=url,
                json=params,
                headers=headers,
                timeout=100
            )
            response.raise_for_status()
            return response.json()
    except httpx.HTTPStatusError as e:
        raise {"error": f"HTTP error {e.response.status_code}: {e.response.text}"}
    except Exception as e:
        raise {"Function execution error": str(e)}