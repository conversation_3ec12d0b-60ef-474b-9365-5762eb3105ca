import csv
import j<PERSON>
from datetime import datetime, timed<PERSON>ta
from io import <PERSON><PERSON>
from typing import Dict, Any, Set
from src.agents.agent_internal.timekeeping_agent.prompts import PROMPT_SYSTEM, PROMPT_RESPONSE
from langfuse.decorators import langfuse_context, observe

class TimekeepingAgent:
    def __init__(self, function_caller, llm):
        self.function_caller = function_caller
        self.llm = llm
        self.last_api_result = None

    def _identify_relevant_fields(self, user_query: str) -> Set[str]:
        query_lower = user_query.lower()
        field_mapping = {
            "check-in": {"datetime", "type"},
            "check-out": {"datetime", "type"},
            "chấm công muộn": {"datetime", "type"},
            "chấm công sớm": {"datetime", "type"},
            "lúc về": {"datetime", "type"},
            "chấm công": {"datetime", "type"},
        }

        relevant_fields = set()
        for keyword, fields in field_mapping.items():
            if keyword in query_lower:
                relevant_fields.update(fields)

        return relevant_fields if relevant_fields else {
            "workday", "total_workday", "overtime_workday",
            "total_minute_check_in_late", "total_minute_checkout_early",
            "minutes_check_in_late_subtracted_by_letter", "minutes_checkout_early_subtracted_by_letter"
        }

    def process_api_result_timekeeping(self, result: Dict, relevant_fields: Set[str]) -> str:
        if not result or not isinstance(result, list) or not result:
            return "No timekeeping data available."

        output = []
        for record in result:
            utc_datetime = datetime.strptime(record["datetime"], "%Y-%m-%dT%H:%M:%S.000Z")
            local_datetime = utc_datetime + timedelta(hours=7)
            date = local_datetime.strftime("%Y-%m-%d")
            time = local_datetime.strftime("%H:%M:%S")
            record_type = record["type"]
            location = record.get("location", "N/A")

            if record_type == "checkin":
                action = "checked in"
            elif record_type == "checkout":
                action = "checked out"
            elif record_type == "import":
                action = "recorded an imported entry"
            else:
                action = f"performed an action ({record_type})"

            entry = f"On {date} at {time}: {action} at location {location}."
            output.append(entry)

        return "\n".join(output) if output else "No relevant timekeeping data found."

    async def analyze_result_api(self, state, timesheet_data: Dict, user_query: str) -> str:
        time_period = state["entities"].get("time_period", {})
        start_date = time_period.get("start_date", "")
        end_date = time_period.get("end_date", "")
        timesheet_csv = ""
        if timesheet_data and "data" in timesheet_data:
            for entry in timesheet_data["data"]:
                timesheet_csv += entry["timesheet_data"]

        prompt_response = PROMPT_RESPONSE.format(
            start_date=start_date,
            end_date=end_date,
            timesheet_csv=timesheet_csv,
            user_query=user_query
        )
        print("Response ", prompt_response)
        response = await self.llm.process_message(
            message=user_query,
            context={},
            system_prompt=prompt_response
        )
        return response

    @observe(name="time_keeping_agent")
    async def execute(self, state: Dict, tools) -> str:
        time_period = state["entities"].get("time_period", {})
        start_date = time_period.get("start_date", "")
        end_date = time_period.get("end_date", "")
        message = f"{state['message']} Start date: {start_date} (YYYY-MM-DD), End date: {end_date} (YYYY-MM-DD)"
        subdomain_tools = tools[state['subdomains'][0]]
        timekeeping_tool = [tool for tool in subdomain_tools if
                          tool['function']['name'] == "checkin-log_find_post"]
        tool_description = (
            f"- '{timekeeping_tool[0]['function']['name']}': {timekeeping_tool[0]['function']['description']} "
            f"(parameters: {json.dumps(timekeeping_tool[0]['function']['parameters']['properties']).replace('{', '{{').replace('}', '}}')})"
        )
        company_id = 1
        employee_id = state["employee_id"]
        prompt_system = PROMPT_SYSTEM.format(
            tools_description=tool_description,
            start_date=start_date,
            end_date=end_date,
            user_query=message,
            company_id=company_id,
            employee_id=employee_id
        )
        # print("Prompt ", prompt_system)

        try:
            response = await self.llm.process_message_with_tools(
                message=message,
                context=state["context"],
                system_prompt=prompt_system,
                tools=timekeeping_tool
            )
        except Exception as e:
            return await self.llm.process_message(
                state["message"], state["context"],
                f"Query: {state['message']}\nError: {str(e)}. Please provide more details, like a name and time period (e.g., month and year)."
            )

        if not response.get("tool_calls"):
            return await self.llm.process_message(
                state["message"], state["context"],
                f"Query: {state['message']}\nNo suitable tools found. Please provide more details, like a name and time period (e.g., month and year), if asking about tardiness or work days."
            )
        tool_calls = response.get("tool_calls")

        results = {"data": []}
        relevant_fields = self._identify_relevant_fields(state["message"])

        for tool_call in tool_calls:
            try:
                params = tool_call['function'].get("arguments")
                tool_api = [tool for tool in subdomain_tools if tool['function']['name'] == tool_call['function'].get("name")][0]
                for param in tool_api['function']['parameters'].get('properties', []):
                    if param not in params:
                        params[param] = tool_api['function']['parameters']['properties'][param].get('default', None)

                params_copy = params.copy()
                params_copy['query']['type']['$in'] = [
                    "checkin",
                    "checkout",
                    "import"
                ]

                function_data = {
                    "path": tool_api['function']['path'],
                    "method": tool_api['function']['method'],
                    "params": params_copy
                }
                result = await self.function_caller.execute(function_data)

                if not result or "error" in result or not result:
                    error_msg = result.get("error", "No data returned") if isinstance(result, dict) else "Unknown error"
                    return await self.llm.process_message(
                        state["message"], state["context"],
                        f"Query: {state['message']}\nFailed due to: {error_msg}. Please provide a specific name and time period (e.g., month and year) for timekeeping or work days."
                    )

                # Process result
                if result:
                    processed_result = self.process_api_result_timekeeping(result, relevant_fields)
                    entry = {
                        "timesheet_data": processed_result
                    }
                    results["data"].append(entry)
                    # print("processed_result ", processed_result)
            except Exception as e:
                return await self.llm.process_message(
                    state["message"], state["context"],
                    f"Query: {state['message']}\nError: {str(e)}. Please provide a specific name and time period (e.g., month and year) for timekeeping or work days."
                )

        if results["data"]:
            self.last_api_result = results
            analysis = await self.analyze_result_api(state, results, message)
            return analysis

        return await self.llm.process_message(
            state["message"], state["context"],
            f"Query: {state['message']}\nNo data found. Please provide a specific name and time period (e.g., month and year) for timekeeping or work days."
        )

