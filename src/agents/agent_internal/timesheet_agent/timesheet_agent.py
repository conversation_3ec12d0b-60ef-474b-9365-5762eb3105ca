import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Set
import asyncio
from concurrent.futures import ThreadPoolExecutor

from src.agents.agent_internal.timesheet_agent.time_sheet_tool import ToolSelector
from langfuse.decorators import langfuse_context, observe

from src.utils.utils import get_total_year_time_periods, get_current_month_time_periods

logger = logging.getLogger(__name__)


class TimesheetAgent:
    def __init__(self, function_caller, llm, max_concurrent_calls: int = 20):
        self.function_caller = function_caller
        self.llm = llm
        self.max_concurrent_calls = max_concurrent_calls
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_calls)
        self.tool_selector = ToolSelector(llm)
        self.cache = {}
        self.BLOCK_MINUTES = 10

    def _identify_relevant_fields(self, user_query: str) -> Set[str]:
        query_lower = user_query.lower()
        field_mapping = {
            "đi muộn": {"total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter"},
            "late": {"total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter"},
            "muộn": {"total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter"},
            "trễ": {"total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter"},
            "đơn đi muộn": {"minutes_check_in_late_subtracted_by_letter"},
            "letter_late": {"minutes_check_in_late_subtracted_by_letter"},
            "về sớm": {"total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter"},
            "early": {"total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter"},
            "sớm": {"total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter"},
            "đơn về sớm": {"minutes_checkout_early_subtracted_by_letter"},
            "letter_early": {"minutes_checkout_early_subtracted_by_letter"},
            "chấm công": {"checkin_detail"},
            "đi làm": {"checkin_detail"},
            "ngày làm việc": {"workday", "total_workday"},
            "workday": {"workday", "total_workday"},
            "làm việc": {"workday", "total_workday"},
            "làm thêm": {"overtime_workday"},
            "tăng ca": {"overtime_workday"},
            "overtime": {"overtime_workday"},
            "nghỉ phép": {"total_paid_leave"},
            "nghỉ không phép": {"total_unpaid_leave"},
            "nghỉ không lý do": {"total_leave_without_reason"},
            "nghỉ": {"total_paid_leave", "total_unpaid_leave", "total_leave_without_reason"},
            "leave": {"total_paid_leave", "total_unpaid_leave", "total_leave_without_reason"},
            "ngày nghỉ": {"total_paid_leave", "total_unpaid_leave", "total_leave_without_reason"}
        }
        relevant_fields = set()
        for keyword, fields in field_mapping.items():
            if keyword in query_lower:
                relevant_fields.update(fields)
        return relevant_fields or {
            "total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter",
            "total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter",
            "workday", "total_workday", "overtime_workday",
            "total_paid_leave", "total_unpaid_leave", "total_leave_without_reason"
        }

    async def fetch_timesheet_data(self, employee_id: str, params: Dict, tool_api: Dict,
                                   semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        cache_key = f"timesheet_{employee_id}_{json.dumps(params, sort_keys=True)}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        async with semaphore:
            for param in tool_api['function']['parameters'].get('properties', []):
                if param not in params:
                    params[param] = tool_api['function']['parameters']['properties'][param].get('default', None)
            params_copy = params.copy()
            function_data = {
                "path": tool_api["function"]["path"],
                "method": tool_api["function"]["method"],
                "params": params_copy
            }
            try:
                result = await self.function_caller.execute(function_data)
                if not result or "error" in result or not result.get("data"):
                    output = {"employee_id": employee_id, "timesheet": [], "month": params["month"],
                              "year": params["year"]}
                else:
                    output = {
                        "employee_id": employee_id,
                        "timesheet": result["data"][0].get("timesheet", []),
                        "month": params["month"],
                        "year": params["year"]
                    }
                self.cache[cache_key] = output
                return output
            except Exception as e:
                logger.error(f"Error fetching timesheet for {employee_id}: {str(e)}")
                return {"employee_id": employee_id, "timesheet": [], "month": params["month"], "year": params["year"]}

    async def fetch_timekeeping_data(self, employee_id: str, params: Dict, tool_api: Dict,
                                     semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        cache_key = f"timekeeping_{employee_id}_{json.dumps(params, sort_keys=True)}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        async with semaphore:
            for param in tool_api['function']['parameters'].get('properties', []):
                if param not in params:
                    params[param] = tool_api['function']['parameters']['properties'][param].get('default', None)
            params_copy = params.copy()
            params_copy["query"]["employee_id"] = employee_id
            function_data = {
                "path": tool_api["function"]["path"],
                "method": tool_api["function"]["method"],
                "params": params_copy
            }
            try:
                result = await self.function_caller.execute(function_data)
                output = []
                start_date = datetime.strptime(params["query"]["datetime"]["$gte"].split("T")[0], "%Y-%m-%d")
                month = start_date.month
                year = start_date.year
                for record in result:
                    try:
                        utc_datetime = datetime.strptime(record["datetime"], "%Y-%m-%dT%H:%M:%S.000Z")
                        local_datetime = utc_datetime + timedelta(hours=7)
                        output.append({
                            "date": local_datetime.strftime("%Y-%m-%d"),
                            "time": local_datetime.strftime("%H:%M:%S"),
                            "type": record["type"],
                            "location": record.get("location", "N/A")
                        })
                    except (KeyError, ValueError) as e:
                        logger.warning(f"Invalid timekeeping record for {employee_id}: {str(e)}")
                        continue
                output = {"employee_id": employee_id, "timekeeping": output, "month": month, "year": year}
                self.cache[cache_key] = output
                return output
            except Exception as e:
                logger.error(f"Error fetching timekeeping for {employee_id}: {str(e)}")
                return {"employee_id": employee_id, "timekeeping": [], "month": params.get("month", 0),
                        "year": params.get("year", 0)}

    def _determine_api_intent(self, query: str) -> str:
        query_lower = query.lower()
        timekeeping_keywords = ["check-in", "check-out", "chấm công", "lúc về"]
        if any(keyword in query_lower for keyword in timekeeping_keywords):
            return "checkin-log_find_post"
        return "employee_list-with-timesheet_post"

    def _generate_timesheet_params(self, state: Dict, employee_name: str) -> List[Dict]:
        time_period = state["entities"].get("time_period", {})
        params_list = []
        if not time_period:
            current_date = datetime.now()
            params_list.append({
                "search": employee_name,
                "month": current_date.month,
                "year": current_date.year,
                "query": {"status": "working"}
            })
            return params_list
        month_year_set = set()
        for period, month_dict in time_period.items():
            for month_key, dates in month_dict.items():
                for date in dates:
                    try:
                        dt = datetime.strptime(date, "%Y-%m-%d")
                        month_year_set.add((dt.month, dt.year))
                    except ValueError:
                        logger.warning(f"Invalid date format: {date}")
                        continue
        for month, year in month_year_set:
            params_list.append({
                "search": employee_name,
                "month": month,
                "year": year,
                "query": {"status": "working"}
            })
        return params_list

    def _generate_timekeeping_params(self, state: Dict, employee_id: str) -> List[Dict]:
        time_period = state["entities"].get("time_period", {})
        params_list = []
        if not time_period:
            current_date = datetime.now()
            start_date = current_date.replace(day=1).strftime("%Y-%m-%d")
            end_date = (current_date.replace(day=1) + timedelta(days=31)).replace(day=1) - timedelta(days=1)
            params_list.append({
                "query": {
                    "employee_id": employee_id,
                    "datetime": {
                        "$gte": f"{start_date}T00:00:00.000Z",
                        "$lte": f"{end_date.strftime('%Y-%m-%d')}T23:59:59.999Z"
                    }
                }
            })
            return params_list
        for period, month_dict in time_period.items():
            all_dates = []
            for month_key, dates in month_dict.items():
                all_dates.extend(dates)
            sorted_days = sorted(all_dates)
            if not sorted_days:
                continue
            try:
                current_start = datetime.strptime(sorted_days[0], "%Y-%m-%d")
                current_end = current_start
                for date in sorted_days[1:]:
                    current_date = datetime.strptime(date, "%Y-%m-%d")
                    if (current_date - current_end).days == 1:
                        current_end = current_date
                    else:
                        params_list.append({
                            "query": {
                                "employee_id": employee_id,
                                "datetime": {
                                    "$gte": current_start.strftime("%Y-%m-%d") + "T00:00:00.000Z",
                                    "$lte": current_end.strftime("%Y-%m-%d") + "T23:59:59.999Z"
                                }
                            }
                        })
                        current_start = current_date
                        current_end = current_date
                params_list.append({
                    "query": {
                        "employee_id": employee_id,
                        "datetime": {
                            "$gte": current_start.strftime("%Y-%m-%d") + "T00:00:00.000Z",
                            "$lte": current_end.strftime("%Y-%m-%d") + "T23:59:59.999Z"
                        }
                    }
                })
            except ValueError as e:
                logger.error(f"Invalid date format in time_period: {e}")
                continue
        return params_list

    def _group_by_month(self, records: List[Dict], date_key: str) -> Dict[str, List[Dict]]:
        result = {}
        for record in records:
            try:
                dt = datetime.strptime(record[date_key], "%Y-%m-%d")
                month_key = f"tháng {dt.month}"
                if month_key not in result:
                    result[month_key] = []
                result[month_key].append(record)
            except ValueError:
                logger.warning(f"Invalid date format in record: {record[date_key]}")
        return result

    @observe(name="time_sheet_agent")
    async def execute(self, state: Dict, tools: Dict, dependencies: Dict[str, Any] = None) -> Dict[str, Any]:
        query = state.get("message", "")
        employee_info = state.get("employee_info", [])
        employees = [{"id": emp["employee_id"], "name": emp.get("name", "Unknown")} for emp in employee_info]
        relevant_fields = self._identify_relevant_fields(query)
        subdomain_tools = tools[state["subdomains"][0]]
        timesheet_tool = next(
            (tool for tool in subdomain_tools if tool["function"]["name"] == "employee_list-with-timesheet_post"), None)
        timekeeping_tool = next(
            (tool for tool in subdomain_tools if tool["function"]["name"] == "checkin-log_find_post"), None)
        if not timesheet_tool or not timekeeping_tool:
            return {"data": "Không tìm thấy công cụ API cần thiết.",
                    "response": "Không tìm thấy công cụ API cần thiết."}
        target_api = self._determine_api_intent(query)
        semaphore = asyncio.Semaphore(self.max_concurrent_calls)
        data = {}
        time_period = state["entities"].get("time_period", {})
        if not time_period:
            if state["intent"] == "leave":
                time_period = get_total_year_time_periods()
                state["entities"]["time_period"] = time_period
            else:
                time_period = get_current_month_time_periods()
                state["entities"]["time_period"] = time_period

        for period in time_period:
            data[period] = {"timesheet": {}, "timekeeping": {}}
        tasks = []
        task_params = []
        for employee in employees:
            emp_id = employee["id"]
            emp_name = employee["name"]
            if target_api == "employee_list-with-timesheet_post":
                params_list = self._generate_timesheet_params(state, emp_name)
                for params in params_list:
                    tasks.append(self.fetch_timesheet_data(emp_id, params, timesheet_tool, semaphore))
                    task_params.append({"type": "timesheet", "month": params["month"], "year": params["year"]})
            params_list = self._generate_timekeeping_params(state, emp_id)
            for params in params_list:
                start_date_str = params["query"]["datetime"]["$gte"].split("T")[0]
                end_date_str = params["query"]["datetime"]["$lte"].split("T")[0]
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d")
                current_date = start_date
                while current_date <= end_date:
                    month = current_date.month
                    year = current_date.year
                    month_start = current_date.replace(day=1)
                    month_end = (month_start + timedelta(days=31)).replace(day=1) - timedelta(days=1)
                    if month_end > end_date:
                        month_end = end_date
                    month_params = {
                        "query": {
                            "employee_id": emp_id,
                            "datetime": {
                                "$gte": month_start.strftime("%Y-%m-%d") + "T00:00:00.000Z",
                                "$lte": month_end.strftime("%Y-%m-%d") + "T23:59:59.999Z"
                            }
                        }
                    }
                    tasks.append(self.fetch_timekeeping_data(emp_id, month_params, timekeeping_tool, semaphore))
                    task_params.append({"type": "timekeeping", "month": month, "year": year})
                    next_month = month_start + timedelta(days=31)
                    current_date = next_month.replace(day=1)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        for params, res in zip(task_params, results):
            if isinstance(res, dict) and isinstance(params, dict):
                month = res.get("month", params.get("month", 0))
                year = res.get("year", params.get("year", 0))
                month_key = f"tháng {month}"
                for period, month_dict in time_period.items():
                    for m_key, date_list in month_dict.items():
                        if m_key != month_key:
                            continue
                        date_set = set(date_list)
                        if "timesheet" in res and params.get("type") == "timesheet":
                            filtered_timesheet = [
                                ts for ts in res.get("timesheet", [])
                                if ts.get("date") in date_set
                            ]
                            if filtered_timesheet:
                                if month_key not in data[period]["timesheet"]:
                                    data[period]["timesheet"][month_key] = []
                                data[period]["timesheet"][month_key].extend(
                                    [{**ts, "employee_id": res["employee_id"]} for ts in filtered_timesheet]
                                )
                        if "timekeeping" in res and params.get("type") == "timekeeping":
                            filtered_timekeeping = [
                                tk for tk in res.get("timekeeping", [])
                                if tk.get("date") in date_set
                            ]
                            if filtered_timekeeping:
                                if month_key not in data[period]["timekeeping"]:
                                    data[period]["timekeeping"][month_key] = []
                                data[period]["timekeeping"][month_key].extend(filtered_timekeeping)

        tool_results = await self.tool_selector.process_api_result(state, data, relevant_fields,
                                                                   ["timesheet", "timekeeping"])
        # print("tool_result", tool_results)
        return {"data": tool_results, "response": tool_results}

        if tool_results:
            return {"data": tool_results, "response": "Good"}
        else:
            return {"data": [], "response": "Bad"}

