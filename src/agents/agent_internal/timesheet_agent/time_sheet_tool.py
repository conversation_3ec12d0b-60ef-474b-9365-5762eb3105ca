import ast
import logging
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from math import ceil
from typing import List, Dict, Set, Callable, Optional

from langfuse.decorators import observe

from src.graph.state import ChatbotState

logger = logging.getLogger(__name__)

FIELD_CONFIG = {
    "total_minute_check_in_late": {
        "description": "Phút đi muộn",
        "format": lambda v: f"Phút đi muộn: {v} ({calculate_blocks(v)} block)",
        "related": ["minutes_check_in_late_subtracted_by_letter"],
    },
    "minutes_check_in_late_subtracted_by_letter": {
        "description": "<PERSON>út đi muộn trừ bởi đơn",
        "format": lambda v: f"Phút đi muộn trừ bởi đơn: {v}",
    },
    "total_minute_checkout_early": {
        "description": "Phút về sớm",
        "format": lambda v: f"Phút về sớm: {v} ({calculate_blocks(v)} block)",
        "related": ["minutes_checkout_early_subtracted_by_letter"],
    },
    "minutes_checkout_early_subtracted_by_letter": {
        "description": "<PERSON>út về sớm trừ bởi đơn",
        "format": lambda v: f"Phút về sớm trừ bởi đơn: {v}",
    },
    "workday": {
        "description": "Ngày làm việc",
        "format": lambda v: f"Ngày làm việc: {v}",
    },
    "total_workday": {
        "description": "Tổng ngày làm việc",
        "format": lambda v: f"Tổng ngày làm việc: {v}",
    },
    "overtime_workday": {
        "description": "Ngày làm thêm",
        "format": lambda v: f"Ngày làm thêm: {v}",
    },
    "total_paid_leave": {
        "description": "nghỉ có phép",
        "format": lambda v: f"Ngày nghỉ có phép: {v}",
    },
    "total_unpaid_leave": {
        "description": "nghỉ không phép",
        "format": lambda v: f"Ngày nghỉ không phép: {v}",
    },
    "total_leave_without_reason": {
        "description": "nghỉ không lý do",
        "format": lambda v: f"Ngày nghỉ không lý do: {v}",
    },
}


def calculate_blocks(minutes: int, block_minutes: int = 10) -> int:
    if not isinstance(minutes, (int, float)) or minutes < 0:
        return 0
    return ceil(minutes / block_minutes) if minutes > 0 else 0


def format_field(field: str, value: int) -> str:
    config = FIELD_CONFIG.get(field, {})
    if not config or value <= 0:
        return ""
    return config["format"](value)


def generate_no_data_response(params: Dict, employee_info: List[Dict]) -> str:
    fields = set(params.get("fields", []))
    employee_id = params.get("employee_id")
    list_date = params.get("list_date", [])
    period = params.get("period", "unknown period")
    if not fields:
        return f"Không có dữ liệu phù hợp"
    if not employee_id:
        return f"Không có nhân viên nào có dữ liệu phù hợp"
    emp_info = next((e for e in employee_info if e["employee_id"] == employee_id), {})
    emp_name = emp_info.get("name", "Nhân viên")
    descriptions = []
    if "total_minute_check_in_late" in fields or "minutes_check_in_late_subtracted_by_letter" in fields:
        descriptions.append(f"Từ {list_date[0].replace('-', '/')} - {list_date[-1].replace('-', '/')} {emp_name} không đi muộn ngày nào")
    if "total_paid_leave" in fields or "total_unpaid_leave" in fields or "total_leave_without_reason" in fields:
        descriptions.append(f"Từ {list_date[0].replace('-', '/')} - {list_date[-1].replace('-', '/')} {emp_name} không nghỉ ngày nào")
    if not descriptions:
        return f"Không có dữ liệu phù hợp trong {period}."
    return "\n".join(descriptions)


@dataclass
class TimesheetRecord:
    date: str
    metrics: Dict[str, int]
    raw_late: int
    net_late: int
    subtracted_late: int
    raw_early: int
    net_early: int
    subtracted_early: int
    check_in: Dict
    check_out: Dict


class TimesheetProcessor:
    def __init__(self, fields: Set[str]):
        self.fields = fields
        self.late_early_fields = {
            "total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter",
            "total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter"
        }

    def process_timesheet(self, ts: Dict, timekeeping: List[Dict]) -> Optional[TimesheetRecord]:
        record_metrics = {field: ts.get(field, 0) for field in self.fields}
        raw_late = net_late = subtracted_late = raw_early = net_early = subtracted_early = 0
        if self.late_early_fields & self.fields:
            net_late = record_metrics.get("total_minute_check_in_late", 0)
            subtracted_late = record_metrics.get("minutes_check_in_late_subtracted_by_letter", 0)
            net_early = record_metrics.get("total_minute_checkout_early", 0)
            subtracted_early = record_metrics.get("minutes_checkout_early_subtracted_by_letter", 0)
            raw_late = net_late + subtracted_late
            raw_early = net_early + subtracted_early
            record_metrics["total_minute_check_in_late"] = raw_late
            record_metrics["total_minute_checkout_early"] = raw_early
        if not any(record_metrics[field] > 0 for field in self.fields):
            return None
        check_in = check_out = {}
        if self.late_early_fields & self.fields:
            check_in = next((tk for tk in timekeeping if tk["date"] == ts["date"]), {})
            check_out = next(
                (tk for tk in sorted(timekeeping, key=lambda x: x["time"], reverse=True) if tk["date"] == ts["date"]),
                {}
            )
        return TimesheetRecord(
            date=ts["date"],
            metrics=record_metrics,
            raw_late=raw_late,
            net_late=net_late,
            subtracted_late=subtracted_late,
            raw_early=raw_early,
            net_early=net_early,
            subtracted_early=subtracted_early,
            check_in=check_in,
            check_out=check_out
        )

    def format_record(self, record: TimesheetRecord, emp_name: str, hr_code: str) -> str:
        lines = [f"{emp_name}:", f"  Ngày: {record.date}"]
        if "total_minute_check_in_late" in self.fields and (record.raw_late > 0 or record.subtracted_late > 0):
            lines.append(f"  Số phút đi muộn: {record.raw_late} ({calculate_blocks(record.raw_late)} block)")
            if record.subtracted_late > 0:
                lines.append(f" Số phút đi muộn trừ bởi đơn: {record.subtracted_late}")
                lines.append(
                    f" Số phút đi muộn sau khi trừ: {record.net_late} ({calculate_blocks(max(0, record.net_late))} block)"
                )
            lines.extend([
                f"  Thời gian check-in: {record.check_in.get('time', 'N/A')}",
                f"  Địa điểm check-in: {record.check_in.get('location', 'N/A')}"
            ])
        if "total_minute_checkout_early" in self.fields and (record.raw_early > 0 or record.subtracted_early > 0):
            lines.append(f"  Phút về sớm: {record.raw_early} ({calculate_blocks(record.raw_early)} block)")
            if record.subtracted_early > 0:
                lines.append(f"  Phút về sớm trừ bởi đơn: {record.subtracted_early}")
                lines.append(
                    f"  Phút về sớm sau khi trừ: {record.net_early} ({calculate_blocks(max(0, record.net_early))} block)"
                )
            lines.extend([
                f"  Thời gian check-out: {record.check_out.get('time', 'N/A')}",
                f"  Địa điểm check-out: {record.check_out.get('location', 'N/A')}"
            ])
        for field, value in record.metrics.items():
            if field not in self.late_early_fields:
                if formatted := format_field(field, value):
                    lines.append(f"  {formatted}")
        return "\n".join(lines)


@observe(name="count_item_tool")
def tool_count(timesheet_data: Dict[str, List[Dict]], timekeeping_data: Dict[str, List[Dict]],
               employee_info: List[Dict], params: Dict) -> str:
    fields = set(params.get("fields", []))
    employee_id = params.get("employee_id")
    period = params.get("period", "unknown period")
    list_date = ast.literal_eval(params.get("period_2").get(f"{period}"))
    params["list_date"] = list_date
    processor = TimesheetProcessor(fields)
    metrics = defaultdict(int)
    emp_summaries = defaultdict(lambda: {
        "name": "Unknown", "hr_code": "N/A", "metrics": defaultdict(int),
        "net_late": 0, "net_early": 0, "count": 0, "count_late": 0, "records": []
    })

    if 'checkin_detail' in fields:
        checkin_details = []
        checkin_logs = {}
        for key, records in timekeeping_data.items():
            daily = defaultdict(list)
            for record in records:
                daily[record["date"]].append((record["time"], record["location"]))
            checkin_logs = {
                date: {
                    "check_in": times[0],
                    "check_out": times[-1]
                }
                for date, times in daily.items()
            }
        for employee_info_item in employee_info:
            for date_item in list_date:
                if date_item in checkin_logs:
                    checkin_details.append(f"""Chi tiết chấm công của {employee_info_item['name']} vào ngày {date_item}:
    Check in: {checkin_logs[f'{date_item}']['check_in'][0]} - Location: {checkin_logs[f'{date_item}']['check_in'][1]}
    Check out: {checkin_logs[f'{date_item}']['check_out'][0]} - Location: {checkin_logs[f'{date_item}']['check_out'][1]}
""")
        return '\n'.join(checkin_details)
    for month_key, timesheets in timesheet_data.items():
        for ts in timesheets:
            emp_id = ts.get("employee_id")
            if employee_id and emp_id != employee_id:
                continue
            emp_info = next((e for e in employee_info if e["employee_id"] == emp_id), {})
            emp_name = emp_info.get("name", "Unknown")
            hr_code = emp_info.get("hr_code", "N/A")
            emp_timekeeping = timekeeping_data.get(month_key, [])
            emp_summaries[emp_id]["name"] = emp_name
            emp_summaries[emp_id]["hr_code"] = hr_code
            record = processor.process_timesheet(ts, emp_timekeeping)
            if not record:
                continue
            emp_summaries[emp_id]["records"].append(processor.format_record(record, emp_name, hr_code))
            for field, value in record.metrics.items():
                metrics[field] += value
                emp_summaries[emp_id]["metrics"][field] += value
                if field == 'total_minute_check_in_late':
                    minutes_check_in_late_subtracted_by_letter = record.metrics.get(
                        'minutes_check_in_late_subtracted_by_letter', 0)
                    metrics["total_blocks"] += calculate_blocks(value)
                    metrics["total_blocks_subtracted_by_letter"] += calculate_blocks(
                        value - minutes_check_in_late_subtracted_by_letter)
                    emp_summaries[emp_id]["metrics"]["total_blocks"] += calculate_blocks(value)
                    emp_summaries[emp_id]["metrics"]["total_blocks_subtracted_by_letter"] += calculate_blocks(value - minutes_check_in_late_subtracted_by_letter)
            if "total_minute_check_in_late" in fields:
                emp_summaries[emp_id]["net_late"] += record.net_late
            if "total_minute_checkout_early" in fields:
                emp_summaries[emp_id]["net_early"] += record.net_early
            emp_summaries[emp_id]["count_late"] += 1 if record.net_late > 0 else 0
            emp_summaries[emp_id]["count"] += 1
    if not any(s["count"] for s in emp_summaries.values()):
        return generate_no_data_response(params, employee_info)
    output = [f"Từ {list_date[0].replace('-', '/')} -> {list_date[-1].replace('-', '/')}:"]
    for field in fields:
        value = metrics.get(field, 0)
        if field == "total_minute_check_in_late":
            raw_late = value
            subtracted_late = metrics.get("minutes_check_in_late_subtracted_by_letter", 0)
            net_late = raw_late - subtracted_late
            total_blocks = metrics.get("total_blocks", 0)
            total_blocks_subtracted_by_letter = metrics.get("total_blocks_subtracted_by_letter", 0)
            # output.append(f"Phút đi muộn: {raw_late} ({total_blocks} block)")
            if subtracted_late > 0:
                output.append(f"Phút đi muộn trừ bởi đơn: {subtracted_late}")
            output.append(f"Phút đi muộn sau khi trừ: {net_late} ({total_blocks_subtracted_by_letter} block)")
        elif field == "total_minute_checkout_early":
            raw_early = value
            subtracted_early = metrics.get("minutes_checkout_early_subtracted_by_letter", 0)
            net_early = raw_early - subtracted_early
            if raw_early > 0:
                output.append(f"Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
            if subtracted_early > 0:
                output.append(f"Phút về sớm trừ bởi đơn: {subtracted_early}")
            if net_early != 0:
                output.append(f"Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
        elif field not in processor.late_early_fields:
            if formatted := format_field(field, value):
                output.append(formatted)
    total_count = sum(s["count"] for s in emp_summaries.values())
    total_count_late = sum(s["count_late"] for s in emp_summaries.values())
    if 'total_minute_check_in_late' in fields:
        output.append(f"Số lần đi muộn: {total_count_late}")
    else:
        output.append(f"Số lần: {total_count}")
    output.append("\nChi tiết từng nhân viên:")
    sorted_summaries = sorted(
        emp_summaries.items(),
        key=lambda x: sum(x[1]["metrics"].values()),
        reverse=True
    )
    for emp_id, summary in sorted_summaries:
        if summary["count"] == 0:
            continue
        emp_lines = [f"{summary['name']} (Mã NV: {summary['hr_code']}):"]
        for field in fields:
            value = summary["metrics"].get(field, 0)
            if field == "total_minute_check_in_late":
                raw_late = value
                subtracted_late = summary["metrics"].get("minutes_check_in_late_subtracted_by_letter", 0)
                net_late = summary["net_late"]
                total_blocks = summary["metrics"].get("total_blocks", 0)
                total_blocks_subtracted_by_letter = summary["metrics"].get("total_blocks_subtracted_by_letter", 0)
                emp_lines.append(f"  Số phút đi muộn: {raw_late} ({total_blocks} block)")
                if subtracted_late > 0:
                    emp_lines.append(f"  Số phút đi muộn trừ bởi đơn: {subtracted_late}")
                emp_lines.append(f"  Số phút đi muộn sau khi trừ: {net_late} ({total_blocks_subtracted_by_letter} block)")
            elif field == "total_minute_checkout_early":
                raw_early = value
                subtracted_early = summary["metrics"].get("minutes_checkout_early_subtracted_by_letter", 0)
                net_early = summary["net_early"]
                if raw_early > 0:
                    emp_lines.append(f"  Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
                if subtracted_early > 0:
                    emp_lines.append(f"  Phút về sớm trừ bởi đơn: {subtracted_early}")
                if net_early != 0:
                    emp_lines.append(
                        f"  Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
            elif field not in processor.late_early_fields:
                if formatted := format_field(field, value):
                    emp_lines.append(f"  {formatted}")

        if 'total_minute_check_in_late' in fields:
            emp_lines.append(f"Số lần đi muộn: {summary['count_late']}")
        else:
            emp_lines.append(f"  Số lần: {summary['count']}")
        output.append("\n".join(emp_lines))
    output.append("\nChi tiết:")
    for emp_id, summary in sorted_summaries:
        for i, record in enumerate(summary["records"]):
            output.append(f"{i + 1}. {record}")
    return "\n".join(output)


@observe(name="aggregate_tool")
def tool_aggregate(timesheet_data: Dict[str, List[Dict]], timekeeping_data: Dict[str, List[Dict]],
                   employee_info: List[Dict], params: Dict) -> str:
    fields = set(params.get("fields", []))
    period = params.get("period", "unknown period")
    target_employee = params.get("employee_id", None)
    department = params.get("department", None)
    processor = TimesheetProcessor(fields)
    stats = defaultdict(lambda: {
        "name": "Unknown",
        "metrics": defaultdict(int),
        "net_late": 0,
        "net_early": 0,
        "count": 0,
        "details": []
    })
    total_metrics = defaultdict(int)
    employees_with_metrics = defaultdict(set)
    filtered_employee_info = employee_info
    if department:
        filtered_employee_info = [
            emp for emp in employee_info if emp.get("department", "").lower() == department.lower()
        ]
        if not filtered_employee_info:
            return f"Không có nhân viên nào trong phòng {department} trong {period}."
    for month_key, timesheets in timesheet_data.items():
        for ts in timesheets:
            emp_id = ts.get("employee_id")
            if emp_id not in [e["employee_id"] for e in filtered_employee_info]:
                continue
            emp_info = next((e for e in employee_info if e["employee_id"] == emp_id), {})
            emp_name = emp_info.get("name", "Unknown")
            emp_timekeeping = timekeeping_data.get(month_key, [])
            stats[emp_id]["name"] = emp_name
            record = processor.process_timesheet(ts, emp_timekeeping)
            if not record:
                continue
            # Only count records with non-zero values for requested fields
            if any(record.metrics.get(field, 0) > 0 for field in fields):
                stats[emp_id]["details"].append(processor.format_record(record, emp_name, ""))
                stats[emp_id]["count"] += 1
            for field, value in record.metrics.items():
                stats[emp_id]["metrics"][field] += value
                total_metrics[field] += value
                if value > 0:
                    employees_with_metrics[field].add(emp_id)
            if "total_minute_check_in_late" in fields:
                stats[emp_id]["net_late"] += record.net_late
            if "total_minute_checkout_early" in fields:
                stats[emp_id]["net_early"] += record.net_early
    if target_employee:
        if target_employee not in stats:
            return f"Không có dữ liệu cho nhân viên {target_employee} trong {period}."
        emp = stats[target_employee]
        output = [f"Thống kê cho {emp['name']} trong {period}:"]
        for field in fields:
            value = emp["metrics"].get(field, 0)
            if field == "total_minute_check_in_late":
                raw_late = value
                subtracted_late = emp["metrics"].get("minutes_check_in_late_subtracted_by_letter", 0)
                net_late = emp["net_late"]
                output.append(f"  Số phút đi muộn: {raw_late} ({calculate_blocks(raw_late)} block)")
                if subtracted_late > 0:
                    output.append(f"  Số phút đi muộn trừ bởi đơn: {subtracted_late}")
                output.append(f"  Số phút đi muộn sau khi trừ: {net_late} ({calculate_blocks(max(0, net_late))} block)")
            elif field == "total_minute_checkout_early":
                raw_early = value
                subtracted_early = emp["metrics"].get("minutes_checkout_early_subtracted_by_letter", 0)
                net_early = emp["net_early"]
                output.append(f"  Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
                if subtracted_early > 0:
                    output.append(f"  Phút về sớm trừ bởi đơn: {subtracted_early}")
                output.append(f"  Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
            elif field not in processor.late_early_fields:
                if formatted := format_field(field, value):
                    output.append(f"  {formatted}")
        output.append(f"  Số lần: {emp['count']}")
        output.append("\nChi tiết:")
        for detail in emp["details"]:
            output.append(f"  {detail}")
        return "\n".join(output)
    if not stats:
        return generate_no_data_response(params, filtered_employee_info)
    total_employees = len(filtered_employee_info)
    output = [
        f"Thống kê tổng hợp trong {period} ({len(stats)}/{total_employees} nhân viên):"
    ]
    total_count = sum(emp["count"] for emp in stats.values())
    for field in fields:
        value = total_metrics.get(field, 0)
        if field == "total_minute_check_in_late":
            raw_late = value
            subtracted_late = total_metrics.get("minutes_check_in_late_subtracted_by_letter", 0)
            net_late = raw_late - subtracted_late
            output.append(f"  Số phút đi muộn: {raw_late} ({calculate_blocks(raw_late)} block)")
            if subtracted_late > 0:
                output.append(f"  Số phút đi muộn trừ bởi đơn: {subtracted_late}")
            output.append(f"  Số phút đi muộn sau khi trừ: {net_late} ({calculate_blocks(max(0, net_late))} block)")
            late_count = len(employees_with_metrics[field])
            output.append(
                f"  Số nhân viên đi muộn: {late_count}/{total_employees} ({late_count / total_employees * 100:.1f}%)")
        elif field == "total_minute_checkout_early":
            raw_early = value
            subtracted_early = total_metrics.get("minutes_checkout_early_subtracted_by_letter", 0)
            net_early = raw_early - subtracted_early
            output.append(f"  Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
            if subtracted_early > 0:
                output.append(f"  Phút về sớm trừ bởi đơn: {subtracted_early}")
            output.append(f"  Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
            early_count = len(employees_with_metrics[field])
            output.append(
                f"  Số nhân viên về sớm: {early_count}/{total_employees} ({early_count / total_employees * 100:.1f}%)")
        elif field not in processor.late_early_fields:
            if formatted := format_field(field, value):
                output.append(f"  {formatted}")
                emp_count = len(employees_with_metrics[field])
                description = FIELD_CONFIG.get(field, {}).get("description", field)
                output.append(
                    f"  Số nhân viên {description.lower()}: {emp_count}/{total_employees} ({emp_count / total_employees * 100:.1f}%)")
    output.append(f"  Số lần: {total_count}")
    for field in fields:
        if field in processor.late_early_fields and field not in {"total_minute_check_in_late",
                                                                  "total_minute_checkout_early"}:
            continue
        top_5 = [
                    emp for emp in sorted(
                stats.values(),
                key=lambda x: x["metrics"].get(field, 0) if field != "total_minute_check_in_late" else x["net_late"],
                reverse=True
            ) if (
                emp["net_late"] > 0 if field == "total_minute_check_in_late" else
                emp["net_early"] > 0 if field == "total_minute_checkout_early" else
                emp["metrics"].get(field, 0) > 0
            )
                ][:5]
        if top_5:
            description = FIELD_CONFIG.get(field, {}).get("description", field)
            output.append(f"\n5 nhân viên {description.lower()} nhiều nhất:")
            for i, emp in enumerate(top_5):
                value = emp["net_late"] if field == "total_minute_check_in_late" else (
                    emp["net_early"] if field == "total_minute_checkout_early" else emp["metrics"].get(field, 0)
                )
                unit = "phút" if field in {"total_minute_check_in_late", "total_minute_checkout_early"} else ""
                blocks = f" ({calculate_blocks(value)} block)" if unit == "phút" else ""
                output.append(
                    f"{i + 1}. {emp['name']}: {value} {unit}{blocks}"
                )
    return "\n".join(output)


@observe(name="extract_most_employee_tool")
def tool_most_employee(timesheet_data: Dict[str, List[Dict]], timekeeping_data: Dict[str, List[Dict]],
                       employee_info: List[Dict], params: Dict) -> str:
    fields = set(params.get("fields", []))
    period = params.get("period", "unknown period")
    processor = TimesheetProcessor(fields)
    result = {
        "name": None, "hr_code": None, "metrics": defaultdict(int),
        "net_late": 0, "net_early": 0, "count": 0, "details": []
    }
    for month_key, timesheets in timesheet_data.items():
        for ts in timesheets:
            emp_id = ts.get("employee_id")
            emp_info = next((e for e in employee_info if e["employee_id"] == emp_id), {})
            emp_name = emp_info.get("name", "Unknown")
            hr_code = emp_info.get("hr_code", "N/A")
            emp_timekeeping = timekeeping_data.get(month_key, [])
            emp_metrics = defaultdict(int)
            emp_net_late = 0
            emp_net_early = 0
            emp_count = 0
            emp_details = []
            record = processor.process_timesheet(ts, emp_timekeeping)
            if not record:
                continue
            emp_details.append(processor.format_record(record, emp_name, hr_code))
            for field, value in record.metrics.items():
                emp_metrics[field] += value
            if "total_minute_check_in_late" in fields:
                emp_net_late += record.net_late
            if "total_minute_checkout_early" in fields:
                emp_net_early += record.net_early
            emp_count += 1
            if emp_count and sum(emp_metrics.values()) > sum(result["metrics"].values()):
                result.update({
                    "name": emp_name,
                    "hr_code": hr_code,
                    "metrics": emp_metrics,
                    "net_late": emp_net_late,
                    "net_early": emp_net_early,
                    "count": emp_count,
                    "details": emp_details
                })
    if not result["count"]:
        return generate_no_data_response(params, employee_info)
    output = [
        f"Nhân viên cao nhất trong {period}: {result['name']} (Mã NV: {result['hr_code']})",
        "Tổng hợp:"
    ]
    for field in fields:
        value = result["metrics"].get(field, 0)
        if field == "total_minute_check_in_late":
            raw_late = value
            subtracted_late = result["metrics"].get("minutes_check_in_late_subtracted_by_letter", 0)
            net_late = result["net_late"]
            output.append(f"  Số phút đi muộn: {raw_late} ({calculate_blocks(raw_late)} block)")
            if subtracted_late > 0:
                output.append(f"  Số phút đi muộn trừ bởi đơn: {subtracted_late}")
            output.append(f"  Số phút đi muộn sau khi trừ: {net_late} ({calculate_blocks(max(0, net_late))} block)")
        elif field == "total_minute_checkout_early":
            raw_early = value
            subtracted_early = result["metrics"].get("minutes_checkout_early_subtracted_by_letter", 0)
            net_early = result["net_early"]
            if raw_early > 0:
                output.append(f"  Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
            if subtracted_early > 0:
                output.append(f"  Phút về sớm trừ bởi đơn: {subtracted_early}")
            if net_early != 0:
                output.append(f"  Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
        elif field not in processor.late_early_fields:
            if formatted := format_field(field, value):
                output.append(f"  {formatted}")
    output.append(f"  Số lần: {result['count']}")
    output.append("  Chi tiết:")
    output.extend(f"    {detail}" for detail in result["details"])
    return "\n".join(output)


@observe(name="extract_most_period_tool")
def tool_most_period(timesheet_data: Dict[str, List[Dict]], timekeeping_data: Dict[str, List[Dict]],
                     employee_info: List[Dict], params: Dict) -> str:
    fields = set(params.get("fields", []))
    granularity = params.get("granularity", "week")
    period = params.get("period", "unknown period")
    processor = TimesheetProcessor(fields)
    periods = defaultdict(lambda: {
        "metrics": defaultdict(int), "net_late": 0, "net_early": 0, "count": 0, "records": []
    })
    for month_key, timesheets in timesheet_data.items():
        for ts in timesheets:
            emp_id = ts.get("employee_id")
            emp_info = next((e for e in employee_info if e["employee_id"] == emp_id), {})
            emp_name = emp_info.get("name", "Unknown")
            hr_code = emp_info.get("hr_code", "N/A")
            emp_timekeeping = timekeeping_data.get(month_key, [])
            record = processor.process_timesheet(ts, emp_timekeeping)
            if not record:
                continue
            try:
                dt = datetime.strptime(ts["date"], "%Y-%m-%d")
                period_key = (
                    f"{dt.year}-{dt.month:02d}-week{(dt.day - 1) // 7 + 1}" if granularity == "week"
                    else ts["date"]
                )
            except ValueError:
                logger.warning(f"Invalid date format: {ts['date']}")
                continue
            periods[period_key]["records"].append(processor.format_record(record, emp_name, hr_code))
            for field, value in record.metrics.items():
                periods[period_key]["metrics"][field] += value
            if "total_minute_check_in_late" in fields:
                periods[period_key]["net_late"] += record.net_late
            if "total_minute_checkout_early" in fields:
                periods[period_key]["net_early"] += record.net_early
            periods[period_key]["count"] += 1
    if not periods:
        return generate_no_data_response(params, employee_info)
    most_period = max(
        periods.items(),
        key=lambda x: sum(x[1]["metrics"].values())
    )
    period_name = (
        f"Tuần {most_period[0].split('-week')[-1]} tháng {most_period[0].split('-')[1]}/{most_period[0].split('-')[0]}"
        if granularity == "week" else f"Ngày {most_period[0]}"
    )
    output = [
        f"Khoảng thời gian cao nhất trong {period}: {period_name}",
        "Tổng hợp:"
    ]
    for field in fields:
        value = most_period[1]["metrics"].get(field, 0)
        if field == "total_minute_check_in_late":
            raw_late = value
            subtracted_late = most_period[1]["metrics"].get("minutes_check_in_late_subtracted_by_letter", 0)
            net_late = most_period[1]["net_late"]
            output.append(f"  Số phút đi muộn: {raw_late} ({calculate_blocks(raw_late)} block)")
            if subtracted_late > 0:
                output.append(f"  Số phút đi muộn trừ bởi đơn: {subtracted_late}")
            output.append(f"  Số phút đi muộn sau khi trừ: {net_late} ({calculate_blocks(max(0, net_late))} block)")
        elif field == "total_minute_checkout_early":
            raw_early = value
            subtracted_early = most_period[1]["metrics"].get("minutes_checkout_early_subtracted_by_letter", 0)
            net_early = most_period[1]["net_early"]
            if raw_early > 0:
                output.append(f"  Phút về sớm: {raw_early} ({calculate_blocks(raw_early)} block)")
            if subtracted_early > 0:
                output.append(f"  Phút về sớm trừ bởi đơn: {subtracted_early}")
            if net_early != 0:
                output.append(f"  Phút về sớm sau khi trừ: {net_early} ({calculate_blocks(max(0, net_early))} block)")
        elif field not in processor.late_early_fields:
            if formatted := format_field(field, value):
                output.append(f"  {formatted}")
    output.append(f"  Số lần: {most_period[1]['count']}")
    output.append("  Chi tiết:")
    output.extend(f"    {record}" for record in most_period[1]["records"])
    return "\n".join(output)


class DataProcessor:
    @staticmethod
    def filter_data(data: Dict[str, List[Dict]], period_days: List[str], data_key: str) -> Dict[str, List[Dict]]:
        if not data or not period_days:
            return {}
        filtered_data = {}
        date_set = set(period_days)
        for month_key, records in data.items():
            filtered_records = [
                item for item in records
                if isinstance(item, dict) and item.get("date") in date_set
            ]
            if filtered_records:
                filtered_data[month_key] = filtered_records
        return filtered_data


class ToolExecutor:
    @staticmethod
    # @observe()
    def execute_tool(
            tool: 'ToolConfig',
            timesheet_data: Dict[str, List[Dict]],
            timekeeping_data: Dict[str, List[Dict]],
            employee_info: List[Dict],
            params: Dict
    ) -> str:
        # if not all([timesheet_data, timekeeping_data, employee_info, params]):
        #     return "Dữ liệu đầu vào không hợp lệ."
        try:
            return tool.function(timesheet_data, timekeeping_data, employee_info, params)
        except Exception as e:
            logger.error(f"Error executing {tool.name}: {str(e)}")
            return f"Lỗi khi xử lý dữ liệu với {tool.name}: {str(e)}"


@dataclass
class ToolConfig:
    name: str
    function: Callable
    description: str
    keywords: List[str]


class ToolSelector:
    def __init__(self, llm):
        self.llm = llm
        self.tools = [
            ToolConfig(
                name="tool_count",
                function=tool_count,
                description="Counts the total number of occurrences for specific metrics (e.g., late check-ins, leaves) for employees in a period, with optional filtering by employee. Returns raw counts and detailed records.",
                keywords=["count", "số lần", "tổng số", "occurrences", "raw count"]
            ),
            ToolConfig(
                name="tool_aggregate",
                function=tool_aggregate,
                description="Provides aggregated statistics for employee metrics, such as total late minutes, percentage of employees late, and top 5 employees for collective queries. Supports both individual and collective statistical summaries.",
                keywords=["thống kê", "tổng hợp", "aggregate", "statistics", "summary"]
            ),
            ToolConfig(
                name="tool_most_employee",
                function=tool_most_employee,
                description="Identifies the single employee with the highest values for metrics like late minutes or leaves in a period. Returns their summarized metrics and detailed records.",
                keywords=["nhiều nhất", "cao nhất", "highest", "most"]
            ),
            ToolConfig(
                name="tool_most_period",
                function=tool_most_period,
                description="Identifies the week or day with the highest values for metrics like late minutes or leaves. Returns summarized metrics and detailed records for that period.",
                keywords=["thời gian", "khoảng thời gian", "period", "week", "day", "most", "highest"]
            )
        ]
        self.data_processor = DataProcessor()
        self.executor = ToolExecutor()

    @observe(name="select_tool")
    async def _match_tool(self, state: ChatbotState) -> ToolConfig:
        """Select the most appropriate tool using LLM or fallback to keyword matching."""
        history = state["context"].get("chat_history", [])
        query = state.get("message", "")
        history_str = "\n".join(
            [f"{chat['role'].capitalize()}: {chat['content']}" for chat in history if chat['role'] == "user"])

        prompt = (
            f"Given chat history: \n'{history_str}'\n"
            f"Given user query: '{query}'\n"
            "Select the most appropriate tool from the following based on their descriptions. "
            "Return only the tool name (e.g., 'tool_aggregate').\n"
            "Consider the following guidelines:\n"
            "- For queries asking for general statistics, summaries, or collective metrics (e.g., 'thống kê', 'tổng hợp', 'aggregate'), choose 'tool_aggregate'.\n"
            "- For queries asking for raw counts or occurrences (e.g., 'số lần', 'count'), choose 'tool_count'.\n"
            "- For queries identifying the highest single employee (e.g., 'nhiều nhất', 'highest'), choose 'tool_most_employee'.\n"
            "- For queries about specific time periods with highest metrics (e.g., 'week', 'day', 'most'), choose 'tool_most_period'.\n\n"
            "Tools:\n"
            "tool_count: Counts the total occurrences of specific metrics (e.g., late check-ins, leaves) for employees in a period, with optional filtering by employee. Returns raw counts and detailed records.\n"
            "tool_aggregate: Provides aggregated statistics for employee metrics, including total late minutes, collective summaries, and top 5 employees for collective queries. Supports both individual and collective queries.\n"
            "tool_most_employee: Identifies the single employee with the highest values for metrics like late minutes or leaves in a period. Returns their summarized metrics and detailed records.\n"
            "tool_most_period: Identifies the week or day with the highest values for metrics like late minutes or leaves. Returns summarized metrics and detailed records for that period.\n"
        )
        try:
            response = await self.llm.process_message(query, {"chat_history": []}, prompt)
            selected_tool_name = response.strip()
            for tool in self.tools:
                if tool.name == selected_tool_name:
                    logger.info(f"LLM selected tool: {tool.name} for query: {query}")
                    return tool
            logger.warning(f"LLM returned invalid tool name: {selected_tool_name}. Falling back to keyword matching.")
        except Exception as e:
            logger.error(f"LLM tool selection failed: {str(e)}. Falling back to keyword matching.")
        query_lower = query.lower()
        best_tool = None
        best_score = 0
        for tool in self.tools:
            score = sum(1 for keyword in tool.keywords if keyword in query_lower)
            if score > best_score:
                best_score = score
                best_tool = tool
        if best_tool:
            logger.info(f"Fallback selected tool: {best_tool.name} for query: {query}")
            return best_tool
        logger.info(f"No tool matched. Defaulting to tool_count for query: {query}")
        return self.tools[0]

    # @observe()
    def _extract_parameters(self, query: str, state: Dict, relevant_fields: Set[str]) -> Dict:
        query_lower = query.lower()
        employee_info = state.get("employee_info", [])
        fields = set()
        field_mappings = {
            ("muộn", "muoonj", "muoonjj", "late"): ["total_minute_check_in_late", "minutes_check_in_late_subtracted_by_letter"],
            ("sớm", "sowms", "sowmss", "early"): ["total_minute_checkout_early", "minutes_checkout_early_subtracted_by_letter"],
            ("làm thêm", "overtime"): ["overtime_workday"],
            ("ngày phép", "nghỉ phép", "có phép", "paid leave"): ["total_paid_leave"],
            ("nghỉ không phép", "không phép", "unpaid leave"): ["total_unpaid_leave"],
            ("nghỉ không lý do", "không lí do", "without reason"): ["total_leave_without_reason"]
        }
        for terms, mapped_fields in field_mappings.items():
            if any(term in query_lower for term in terms):
                fields.update(mapped_fields)
        if not fields:
            fields = relevant_fields
        params = {
            "fields": list(fields),
            "employee_id": None,
            "granularity": "day" if "day" in query_lower else "week",
            "period": state.get("entities", {}).get("time_period", {}).get("period", "unknown period")
        }
        for emp in employee_info:
            # if emp.get("name", "").lower() in query_lower or emp.get("employee_id", "").lower() in query_lower:
            if emp.get("name", "").lower() in query_lower:
                params["employee_id"] = emp["employee_id"]
                break
        return params

    # @observe()
    async def process_api_result(
            self,
            state: Dict,
            data: Dict[str, Dict[str, Dict[str, List[Dict]]]],
            relevant_fields: Set[str],
            data_keys: List[str] = ["timesheet", "timekeeping"]
    ) -> str:
        query = state.get("message", "")
        if not query:
            return "Không có câu hỏi nào được cung cấp."
        time_period = state.get("entities", {}).get("time_period", {})
        if not time_period:
            logger.warning("No time_period found in state")
            return "Không tìm thấy thông tin thời gian để xử lý."

        selected_tool = await self._match_tool(state)
        params = self._extract_parameters(query, state, relevant_fields)
        employee_info = state.get("employee_info", [])
        result_lines = []
        for period, month_dict in time_period.items():
            if not month_dict:
                result_lines.append(generate_no_data_response(params, employee_info))
                continue
            for month_key, dates in month_dict.items():
                if not dates:
                    result_lines.append(generate_no_data_response({**params, "period": month_key}, employee_info))
                    continue
                filtered_data = {}
                for data_key in data_keys:
                    period_data = data.get(period, {}).get(data_key, {})
                    filtered_data[data_key] = self.data_processor.filter_data(period_data, dates, data_key)
                if not any(filtered_data.values()):
                    result_lines.append(generate_no_data_response({**params, "period": month_key}, employee_info))
                    continue
                params["period"] = month_key
                params["period_2"] = {
                    f"{month_key}": f"{month_dict[f'{month_key}']}"
                }
                result = self.executor.execute_tool(
                    selected_tool,
                    filtered_data.get("timesheet", {}),
                    filtered_data.get("timekeeping", {}),
                    employee_info,
                    params
                )
                # result_lines.append(f"Kết quả cho {month_key}:\n{result}")
                result_lines.append(f"{result}")
        return "\n\n".join(result_lines) if result_lines else generate_no_data_response(params, employee_info)
