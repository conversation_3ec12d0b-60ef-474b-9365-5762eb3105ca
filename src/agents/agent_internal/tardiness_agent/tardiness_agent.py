import json
import logging
from typing import Dict, Set
from src.agents.agent_internal.tardiness_agent.prompts import PROMPT_SYSTEM, PROMPT_RESPONSE, PROMPT_RESPONSE_VN
from langfuse.decorators import langfuse_context, observe

logger = logging.getLogger(__name__)
class TardinessAgent:
    def __init__(self, function_caller, llm):
        self.function_caller = function_caller
        self.llm = llm
        self.last_api_result = None

    def _identify_relevant_fields(self, user_query: str) -> Set[str]:
        query_lower = user_query.lower()
        field_mapping = {
            "đi muộn": {"total_minute_check_in_late"},
            "late": {"total_minute_check_in_late"},
            "về sớm": {"total_minute_checkout_early"},
            "early": {"total_minute_checkout_early"},
            "đơn đi muộn": {"minutes_check_in_late_subtracted_by_letter"},
            "đơn về sớm": {"minutes_checkout_early_subtracted_by_letter"},
            "letter_late": {"minutes_check_in_late_subtracted_by_letter"},
            "letter_early": {"minutes_checkout_early_subtracted_by_letter"},
            "ngày làm việc": {"workday", "total_workday"},
            "workday": {"workday", "total_workday"},
            "làm thêm": {"overtime_workday"},
            "overtime": {"overtime_workday"}
        }

        relevant_fields = set()
        for keyword, fields in field_mapping.items():
            if keyword in query_lower:
                relevant_fields.update(fields)

        return relevant_fields if relevant_fields else {
            "workday", "overtime_workday", "total_workday",
            "total_minute_check_in_late", "total_minute_checkout_early",
            "minutes_check_in_late_subtracted_by_letter", "minutes_checkout_early_subtracted_by_letter"
        }

    def process_api_result_tardiness(self, result: Dict, relevant_fields: Set[str]) -> str:
        if not result or "data" not in result or not result["data"]:
            return "No tardiness data available."

        result_timesheets = result["data"][0].get("timesheet", [])
        if not result_timesheets:
            return "No timesheet records found."

        output = []
        for timesheet in result_timesheets:
            date = timesheet["date"]
            details = []

            for field in relevant_fields:
                value = timesheet.get(field, 0)
                if value > 0:
                    if field == "total_minute_check_in_late":
                        details.append(f"checked in late by {value} minutes")
                    elif field == "total_minute_checkout_early":
                        details.append(f"checked out early by {value} minutes")
                    elif field == "minutes_check_in_late_subtracted_by_letter":
                        details.append(f"{value} minutes of late check-in excused by a letter")
                    elif field == "minutes_checkout_early_subtracted_by_letter":
                        details.append(f"{value} minutes of early checkout excused by a letter")
                    elif field == "workday":
                        details.append(f"worked for {value} days")
                    elif field == "total_workday":
                        details.append(f"total workdays recorded as {value}")
                    elif field == "over-time_workday":
                        details.append(f"worked overtime for {value} days")

            if details:
                entry = f"On {date}: " + "; ".join(details) + "."
                output.append(entry)

        return "\n".join(output) if output else "No relevant tardiness or work data found."

    async def analyze_result_api(self, state, timesheet_data: Dict, user_query: str) -> str:
        time_period = state["entities"].get("time_period", {})
        start_date = time_period.get("start_date", "")
        end_date = time_period.get("end_date", "")
        timesheet_by_month = ""
        if timesheet_data and "data" in timesheet_data:
            for entry in timesheet_data["data"]:
                month = entry["month"]
                year = entry["year"]
                timesheet_csv = entry["timesheet_data"]
                timesheet_by_month += f"\nData for {month}/{year}:\n{timesheet_csv}\n"

        prompt_response = PROMPT_RESPONSE.format(
            timesheet_by_month=timesheet_by_month,
            user_query=user_query,
            start_date=start_date,
            end_date=end_date,
        )
        print("Response ", prompt_response)
        response = await self.llm.process_message(
            message=user_query,
            context={},
            system_prompt=prompt_response
        )
        return response

    @observe(name="tardiness_agent")
    async def execute(self, state: Dict, tools) -> str:
        time_period = state["entities"].get("time_period", {})
        start_date = time_period.get("start_date", "")
        end_date = time_period.get("end_date", "")
        message = (
            f"{state['message']} "
            f"(Time period specified - Start date: {start_date} (YYYY-MM-DD), End date: {end_date} (YYYY-MM-DD))"
            if start_date and end_date
            else f"{state['message']} (No time period specified)"
        )
        subdomain_tools = tools[state['subdomains'][0]]
        tardiness_tool = [tool for tool in subdomain_tools if tool['function']['name'] == "employee_list-with-timesheet_post"]
        tool_description = (
            f"- '{tardiness_tool[0]['function']['name']}': {tardiness_tool[0]['function']['description']} "
            f"(parameters: {json.dumps(tardiness_tool[0]['function']['parameters']['properties']).replace('{', '{{').replace('}', '}}')})"
        )
        system_prompt = PROMPT_SYSTEM.format(
            tools_description=tool_description,
            start_date=start_date,
            end_date=end_date,
            user_query=message
        )
        print("Prompt ", system_prompt)

        try:
            response = await self.llm.process_message_with_tools(
                message=message,
                context=state["context"],
                system_prompt=system_prompt,
                tools=tardiness_tool
            )
        except Exception as e:
            return await self.llm.process_message(
                state["message"], state["context"],
                f"Query: {state['message']}\nError: {str(e)}. Please provide more details, like a name and time period (e.g., month and year)."
            )
        if not response.get("tool_calls"):
            return await self.llm.process_message(
                state["message"], state["context"],
                f"Query: {state['message']}\nNo suitable tools found. Please provide more details, like a name and time period (e.g., month and year), if asking about tardiness or work days."
            )
        tool_calls = response.get("tool_calls")

        results = {"data": []}
        relevant_fields = self._identify_relevant_fields(state["message"])
        for tool_call in tool_calls:
            try:
                params = tool_call['function'].get("arguments")
                tool_api = [tool for tool in subdomain_tools if tool['function']['name'] == tool_call['function'].get("name")][0]
                for param in tool_api['function']['parameters'].get('properties', []):
                    if param not in params:
                        params[param] = tool_api['function']['parameters']['properties'][param].get('default', None)

                params_copy = params.copy()
                params_copy['query']['status'] = 'working'
                function_data = {
                    "path": tool_api['function']['path'],
                    "method": tool_api['function']['method'],
                    "params": params_copy
                }
                result = await self.function_caller.execute(function_data)

                if not result or "error" in result or not result.get("data"):
                    error_msg = result.get("error", "No data returned") if isinstance(result, dict) else "Unknown error"
                    return await self.llm.process_message(
                        state["message"], state["context"],
                        f"Query: {state['message']}\nFailed due to: {error_msg}. Please provide a specific name and time period (e.g., month and year) for tardiness or work days."
                    )

                if result and "data" in result:
                    if len(result['data'][0]['timesheet']) == 0:
                        return await self.llm.process_message(
                            state["message"], state["context"],
                            f"Query: {state['message']}\nNo data found. Please provide a specific name and time period (e.g., month and year) for tardiness or work days."
                        )

                    processed_result = self.process_api_result_tardiness(result, relevant_fields)
                    # print("result_api", processed_result)
                    entry = {
                        "month": params["month"],
                        "year": params["year"],
                        "timesheet_data": processed_result
                    }
                    results["data"].append(entry)

            except Exception as e:
                return await self.llm.process_message(
                    state["message"], state["context"],
                    f"Query: {state['message']}\nError: {str(e)}. Please provide a specific name and time period (e.g., month and year) for tardiness or work days."
                )

        if results["data"]:
            self.last_api_result = results
            analysis = await self.analyze_result_api(state, results, message)
            return analysis

        return await self.llm.process_message(
            state["message"], state["context"],
            f"Query: {state['message']}\nNo data found. Please provide a specific name and time period (e.g., month and year) for tardiness or work days."
        )
