import os
from dotenv import load_dotenv

load_dotenv()
from src.models.llm_config import LLMConfig
from src.agents.tool_call_agent import ToolCallAgent

# class base to agent class inherit
class AgentManager:
    def __init__(
        self,
        inital_message: str,
        system_prompt:str,
        message,
        agent_tools: list,
        tool_excutor
    ):
        self.inital_message = inital_message
        self.system_prompt = system_prompt
        self.message = message
        self.agent_tools = agent_tools
        self.tool_excutor = tool_excutor
        self.initialize_llm()
        self.chat_agent = self._create_chat_agent()

    def initialize_llm(self):
        model_name = os.getenv('MODEL_NAME')
        api_key = os.getenv('API_KEY')
        base_url = os.getenv('BASE_URL')
        self.llm_config = LLMConfig(
            model_name = model_name,
            api_key = api_key,
            base_url = base_url
        )

    def _create_chat_agent(self):
        chat_agent = ToolCallAgent(
            messages=self.message,
            llm_config=self.llm_config,
            tool_dict=self.agent_tools,
            tools=self.tool_excutor,
            agent_tools=self.agent_tools,
            tool_excutor=self.tool_excutor
        )
        return chat_agent

    async def generate_response(self, user_input, **kwargs):
        response = self.chat_agent.on_message(user_input, **kwargs)
        return response
    