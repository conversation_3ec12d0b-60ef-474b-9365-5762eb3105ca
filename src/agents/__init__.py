#
# from agent_internal.tardiness_agent.tardiness_agent import TardinessAgent
# from agent_internal.timekeeping_agent.timekeeping_agent import TimekeepingAgent
#
#
# AGENT_MAPPING = {
#     "Tardiness": TardinessAgent,
#     "Timekeeping": TimekeepingAgent
#
# }
#
# def get_agent_module(agent_name):
#     try:
#         return AGENT_MAPPING[agent_name]
#     except KeyError:
#         raise ValueError("Unknown agent module {}".format(agent_name))