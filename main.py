import asyncio
import logging
import os

import gradio as gr

from src.core.chatbot_core import Cha<PERSON><PERSON><PERSON><PERSON>, ChatbotConfig
from langfuse.decorators import langfuse_context, observe

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Khởi tạo chatbot
config = ChatbotConfig(
    llm_type={"paid": "openai", "free": "gemini"},
    api_base_url="https://api.weekly.vn",
    max_context_length=10,
    timeout=1000,
    human_in_loop_threshold=5,
    max_retries=5,
    rag_docs_path="./internal_docs",
    db_uri="sqlite:///db.sqlite",
    max_chat_history=3,
)
chatbot = ChatbotCore(config, domains_dir="./src/domains")
user_id = "user123"


# Hàm xử lý tin nhắn cho Gradio
@observe()
async def chatbot_response(message, history):
    try:
        print("Message: ", message)
        response = await chatbot.process_request(message, user_id)
        return response
    except Exception as e:
        return f"Tôi không hiểu câu hỏi. Vui lòng cung cấp thông tin chi tiết hơn"


def gradio_wrapper(message, history):
    response = asyncio.run(chatbot_response(message, history))
    if history is None:
        history = []
    updated_history = history + [[message, response]]
    return updated_history, ""


# Danh sách gợi ý câu hỏi mẫu
SAMPLE_QUESTIONS = [
    "So sánh tình hình đi muộn trong tháng 3 của Nguyễn Tiến Anh với tình hình đi muộn trong tháng 5 của Ngô Gia Phong",
    "Thống kê tình hình đi muộn của Ngô Gia Phong trong tháng 3 và số lần nghỉ phép của Nguyễn Tiến Anh trong tháng 5",
    "Công ty có bao nhiêu người",
    "Danh sách các phòng ban trong công ty",
    "Ban giám đốc gồm những ai",
    "Có bao nhiêu nhân viên full-time ở D2",
    "Chi tiết về hồ sơ nhân sự của Nguyễn Tiến Anh",
    "Chi tiết về hồ sơ nhân sự của Ngô Ngọc Cường",
    "Tôi muốn biết chi tiết thông tin của Nguyễn Tiến Anh, Ngô Gia Phong",
    "Chi tiết về hồ sơ nhân viên trong phòng nhân sự",
    "Chi tiết về hồ sơ nhân viên trong D2",
    "Thống kê đi muộn của D2 từ đầu năm đến giờ",
    "Thống kê đi muộn của D2 từ đầu năm đến tháng 7/2025",
    "Nguyễn Công Trí đã làm ở công ty được mấy năm",
    "Nguyễn Tiến Anh và Ngô Gia Phong đã làm việc ở công ty được mấy năm?",
    "Tuần trước Nguyễn Tiến Anh có đi muộn không?",
    "Nguyễn Tiến Anh đi muộn bao nhiêu lần trong tháng 3?",
    "Thống kê đi muộn phòng nhân sự từ đầu năm đến giờ",
    "Nguyễn Tiến Anh còn mấy ngày nghỉ phép",
    "Số phép tồn của Ngô Gia Phong",
    "Thống kê nghỉ phép của Nguyễn Tiến Anh từ đầu năm đến giờ?",
    "Thống kê nghỉ phép của Nguyễn Công Trí từ đầu năm đến giờ?",
    "thống kê nghỉ phép từ đầu nằm đến giờ phòng nhân sự",
    "Thống kê nghỉ phép phòng nhân sự 2024",
    "Tháng 1/2025 Ngô Gia Phong đi muộn mấy lần",
    "Thống kê đi muộn của D2 tuần trước",
    "Thống kê đi muộn của D2 từ 02/06/2025 đến 08/06/2025",
    "Thống kê đi muộn của phòng nhân sự tháng này",
    "Thống kê đi muộn tháng 1, tháng 2 của Ngô Gia Phong",
    "So sánh đi muộn phòng nhân sự tháng này với tháng trước"
]


# Hàm xử lý khi chọn gợi ý câu hỏi
def handle_sample_question(question, history):
    updated_history, _ = gradio_wrapper(question, history)
    return updated_history, question


def clear_conversation():
    chatbot.clear_chat_history(user_id)
    return "", []


with gr.Blocks(title="HR Chatbot Demo") as demo:
    gr.Markdown("""## 🤖 Chào mừng bạn đến với Hệ thống Chatbot Nhân sự thông minh!""")
    
    gr.Markdown("### 📋 **Các chức năng hỗ trợ:**")
    
    with gr.Row():
        with gr.Column():
            gr.Markdown("""
            **🏢 Thông tin công ty & tổ chức**
            - Tra cứu số lượng nhân viên, cơ cấu tổ chức
            - Thông tin các phòng ban và bộ phận trong công ty
            
            **⏰ Chấm công & thời gian làm việc**
            - Thống kê đi muộn của nhân viên
            - Tra cứu lịch sử chấm công và thời gian làm việc
            """)
        
        with gr.Column():
            gr.Markdown("""
            **🏖️ Thông tin nghỉ phép**
            - Xem lịch sử nghỉ phép của nhân viên
            - Thống kê các loại nghỉ phép đã sử dụng

            **📊 Số dư phép tồn**
            - Kiểm tra số ngày phép còn lại của nhân viên
            - Tính toán phép năm và các loại phép khác
            """)
        with gr.Column():
            gr.Markdown("""
            **👤 Hồ sơ nhân sự**
            - Tra cứu thông tin cá nhân của nhân viên
            - Chi tiết về vị trí, bộ phận, thâm niên làm việc
            
            **🏛️ Thông tin phòng ban**
            - Cơ cấu tổ chức và danh sách nhân viên theo phòng ban
            - Thông tin quản lý và cấu trúc bộ phận
            """)
    
    gr.Markdown("""
    ### 💡 **Mẹo sử dụng:**
    - Đặt câu hỏi rõ ràng về tên người, tên phòng ban và cụ thể về thời gian để nhận được kết quả chính xác nhất
    - **Khuyến nghị:** Xóa lịch sử trò chuyện sau 3-4 câu hỏi để đảm bảo hiệu suất tối ưu
    - Sử dụng các gợi ý câu hỏi bên dưới để tham khảo cách đặt câu hỏi hiệu quả
    
    ---
    """)

    # Define chatbot and textbox first
    chatbot_interface = gr.Chatbot(label="Cuộc trò chuyện", height=450)
    msg = gr.Textbox(
        placeholder="Nhập câu hỏi của bạn tại đây (ví dụ: 'Ngô Gia Phong đi muộn bao nhiêu lần trong tháng 3?')",
        label="Tin nhắn của bạn"
    )

    # Accordion chứa các nút gợi ý câu hỏi
    with gr.Accordion("Gợi ý câu hỏi", open=False):
        for question in SAMPLE_QUESTIONS:
            gr.Button(question).click(
                fn=handle_sample_question,
                inputs=[gr.State(value=question), chatbot_interface],
                outputs=[chatbot_interface, msg]
            )

    # Clear button
    clear = gr.Button("Xóa cuộc trò chuyện")

    # Gửi tin nhắn thủ công
    msg.submit(gradio_wrapper, [msg, chatbot_interface], [chatbot_interface, msg])

    # Xóa cuộc trò chuyện
    clear.click(clear_conversation, None, [msg, chatbot_interface])

if __name__ == "__main__":
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=True
    )
