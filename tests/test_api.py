from src.modules.function_caller import FunctionCaller
import asyncio

api_func = FunctionCaller()
function_data = {
    "path": "/employee/list",
    "method": "post",
    "params": {
        "search": "<PERSON><PERSON><PERSON><PERSON>",
        "searchFields": ["hr_code", "name", "email"]
    }
}


async def main():
    data = await api_func.execute(function_data)
    print(data)

asyncio.run(main())
