#!/usr/bin/env python3
"""
Simple script to analyze a single question using the analyze_question function.

Usage:
    python tests/analyze_single_question.py "Your question here"
    
Example:
    python tests/analyze_single_question.py "Nguyễn Tiến Anh đi muộn bao nhiêu lần trong tháng 3?"
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph.planning import QuestionAnalyzer


class MockLLM:
    """Mock LLM provider for testing purposes"""
    
    async def process_message(self, message: str, context: Dict, prompt: str) -> str:
        """
        Mock implementation that returns a sample JSON response based on the question type.
        """
        message_lower = message.lower()
        
        if "công ty có bao nhiêu" in message_lower:
            return '''[{
                "intent": "statistics",
                "scope": {"type": "company", "value": "all"},
                "entities": {"metric": "count"}
            }]'''
        elif "phòng ban" in message_lower and "danh sách" in message_lower:
            return '''[{
                "intent": "statistics", 
                "scope": {"type": "company", "value": "all"},
                "entities": {"metric": "department_list"}
            }]'''
        elif "đi muộn" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô gia phong", "nguyễn công trí"]):
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
                time_period = "tháng 3" if "tháng 3" in message_lower else "từ đầu năm đến giờ"
                return f'''[{{
                    "intent": "timesheet",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"time_period": "{time_period}"}}
                }}]'''
            elif any(dept in message_lower for dept in ["d2", "phòng nhân sự"]):
                dept_name = "Phòng Sản Xuất D2" if "d2" in message_lower else "Phòng nhân sự"
                return f'''[{{
                    "intent": "timesheet",
                    "scope": {{"type": "department", "value": "{dept_name}"}},
                    "entities": {{"time_period": "từ đầu năm đến giờ"}}
                }}]'''
        elif "nghỉ phép" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô gia phong", "nguyễn công trí"]):
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
                return f'''[{{
                    "intent": "leave",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"time_period": "từ đầu năm đến giờ"}}
                }}]'''
        elif "phép tồn" in message_lower or "còn mấy ngày nghỉ phép" in message_lower:
            name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
            return f'''[{{
                "intent": "remain_leave",
                "scope": {{"type": "individual", "value": "{name}"}},
                "entities": {{}}
            }}]'''
        elif "hồ sơ" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô ngọc cường"]):
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Ngọc Cường"
                return f'''[{{
                    "intent": "statistics",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"metric": "list_details"}}
                }}]'''
            elif "phòng nhân sự" in message_lower:
                return '''[{
                    "intent": "statistics",
                    "scope": {"type": "department", "value": "Phòng nhân sự"},
                    "entities": {"metric": "list_details"}
                }]'''
        
        # Default fallback response
        return '''[{
            "intent": "casual",
            "scope": {"type": "company", "value": ""},
            "entities": {"error": "Unable to analyze question"}
        }]'''


def create_mock_sub_agents() -> Dict[str, Dict[str, Any]]:
    """Create mock sub_agents structure for testing"""
    return {
        "attendance": {
            "timesheet": {
                "description": "Handles queries about employee attendance, including late check-ins, early check-outs, absences, and leave days."
            }
        },
        "payroll": {
            "payroll": {
                "description": "Handles queries about employee salary and compensation."
            }
        },
        "leave": {
            "leave": {
                "description": "Handles queries about employee leave days and vacation time."
            }
        },
        "performance": {
            "performance": {
                "description": "Handles queries about employee performance metrics and evaluations."
            }
        }
    }


def create_test_state(question: str) -> Dict[str, Any]:
    """Create a test state dictionary for the analyze_question function"""
    return {
        "message": question,
        "context": {
            "chat_history": []
        }
    }


async def analyze_single_question(question: str):
    """Analyze a single question and display the result"""
    print("=" * 80)
    print("SINGLE QUESTION ANALYSIS")
    print("=" * 80)
    print(f"Question: {question}")
    print("-" * 80)
    
    # Initialize components
    mock_llm = MockLLM()
    mock_sub_agents = create_mock_sub_agents()
    
    # Create QuestionAnalyzer instance
    analyzer = QuestionAnalyzer(llm=mock_llm, sub_agents=mock_sub_agents)
    
    try:
        # Create state for this question
        state = create_test_state(question)
        
        # Analyze the question
        analysis_result = await analyzer.analyze_question(state)
        
        # Pretty print the JSON result
        formatted_result = json.dumps(analysis_result, indent=2, ensure_ascii=False)
        print("Analysis Result:")
        print(formatted_result)
        print("=" * 80)
        
        return analysis_result
        
    except Exception as e:
        error_msg = f"Error analyzing question: {str(e)}"
        print(f"❌ {error_msg}")
        print("=" * 80)
        return None


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python tests/analyze_single_question.py \"Your question here\"")
        print("\nExample:")
        print("python tests/analyze_single_question.py \"Nguyễn Tiến Anh đi muộn bao nhiêu lần trong tháng 3?\"")
        sys.exit(1)
    
    question = sys.argv[1]
    
    try:
        result = asyncio.run(analyze_single_question(question))
        sys.exit(0 if result is not None else 1)
    except KeyboardInterrupt:
        print("\n\nAnalysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
