# Question Analysis Test Script

This directory contains a test script that extracts questions from `main.py` and analyzes them using the `analyze_question` function from the planning module.

## Files

- `test_question_analysis.py` - Main test script
- `question_analysis_results.json` - Generated results file (created after running the test)
- `README_question_analysis.md` - This documentation file

## Features

The test script provides the following functionality:

1. **Automatic Question Extraction**: Dynamically extracts questions from the `SAMPLE_QUESTIONS` list in `main.py`
2. **Mock LLM Support**: Uses a mock LLM provider for testing without requiring API keys
3. **Real LLM Support**: Can optionally use real LLM providers (requires API keys)
4. **Comprehensive Error Handling**: Handles failures gracefully and reports errors
5. **JSON Output**: Pretty-prints analysis results and saves them to a JSON file
6. **Progress Tracking**: Shows progress and summary statistics
7. **Flexible Testing**: Supports limiting the number of questions for quick testing

## Usage

### Basic Usage (Mock LLM)

```bash
# Activate virtual environment
source .venv/bin/activate

# Run with mock LLM (no API keys required)
python tests/test_question_analysis.py
```

### Advanced Usage

```bash
# Use real LLM (requires API keys to be configured)
python tests/test_question_analysis.py --real-llm

# Test only first 5 questions for quick testing
python tests/test_question_analysis.py --limit 5

# Combine options
python tests/test_question_analysis.py --real-llm --limit 10
```

### Command Line Options

- `--real-llm`: Use real LLM instead of mock (requires API keys)
- `--limit N`: Limit testing to first N questions

## Requirements

The script requires the following dependencies (available in the project's virtual environment):

- `asyncio` (built-in)
- `json` (built-in)
- `logging` (built-in)
- `pydantic` (from requirements)
- Project modules:
  - `src.graph.planning.QuestionAnalyzer`
  - `src.llm_providers.llm_provider.LLMProvider`

## Output

### Console Output

The script provides detailed console output showing:

1. Test configuration (mock vs real LLM, number of questions)
2. Progress for each question:
   - Question text
   - Analysis result (pretty-printed JSON)
   - Success/error status
3. Summary statistics:
   - Total questions processed
   - Success/failure counts
   - Success rate percentage

### JSON Results File

Results are saved to `question_analysis_results.json` with the following structure:

```json
[
  {
    "question": "Question text here",
    "analysis": {
      "intent": "statistics|timesheet|leave|remain_leave|performance|casual",
      "scope": {
        "type": "individual|department|company",
        "value": "specific name or 'all'"
      },
      "entities": {
        "metric": "count|list_details|etc",
        "time_period": "specific time period"
      }
    },
    "status": "success|error",
    "error": "error message if status is error"
  }
]
```

## Mock LLM Behavior

The mock LLM uses simple heuristics to analyze questions:

- **Company statistics**: Questions about company size, departments
- **Individual timesheet**: Questions about specific employees and attendance
- **Department timesheet**: Questions about department attendance
- **Leave queries**: Questions about vacation/leave time
- **Remaining leave**: Questions about remaining vacation days
- **Employee profiles**: Questions about employee details
- **Fallback**: Returns "casual" intent for unrecognized patterns

## Real LLM Usage

When using `--real-llm`, the script attempts to use the actual LLM providers configured in the project. This requires:

1. Proper API keys configured in environment variables
2. Network connectivity
3. Valid LLM provider configuration

If real LLM initialization fails, the script automatically falls back to mock LLM.

## Error Handling

The script includes comprehensive error handling:

- **Import errors**: Graceful handling of missing dependencies
- **File access errors**: Fallback to hardcoded questions if main.py is inaccessible
- **LLM errors**: Individual question failures don't stop the entire test
- **JSON parsing errors**: Malformed responses are caught and reported
- **Network errors**: Real LLM failures are handled gracefully

## Example Output

```
================================================================================
QUESTION ANALYSIS TEST
================================================================================
Testing 30 questions from main.py
Using MOCK LLM for analysis
================================================================================

[ 1/30] Processing question:
Question: Công ty có bao nhiêu người
------------------------------------------------------------
Analysis Result:
{
  "intent": "statistics",
  "scope": {
    "type": "company",
    "value": "all"
  },
  "entities": {
    "metric": "count"
  }
}
============================================================

...

================================================================================
SUMMARY
================================================================================
Total questions processed: 30
Successful analyses: 30
Failed analyses: 0
Success rate: 100.0%

Results saved to: question_analysis_results.json
```

## Troubleshooting

### Common Issues

1. **ModuleNotFoundError**: Make sure to activate the virtual environment
2. **Permission errors**: Ensure write permissions for the results file
3. **API key errors**: When using `--real-llm`, ensure API keys are properly configured
4. **Import errors**: Verify all project dependencies are installed

### Debug Mode

For more detailed logging, you can modify the logging level in the script:

```python
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## Integration

This test script can be integrated into CI/CD pipelines or used for:

- **Regression testing**: Ensure question analysis behavior remains consistent
- **Performance testing**: Measure analysis speed and accuracy
- **Development testing**: Quick validation during development
- **Documentation**: Generate examples of question analysis results
