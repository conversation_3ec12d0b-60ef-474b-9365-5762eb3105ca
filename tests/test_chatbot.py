# tests/test_chatbot_core.py
import pytest
import logging
from unittest.mock import AsyncMock, patch, MagicMock
from src.core.chatbot_core import Chatbot<PERSON><PERSON>, State
from src.core.graph_v1 import chatbot_graph

logging.basicConfig(level=logging.ERROR)


@pytest.fixture
def mock_config():
    """Fixture cung cấp config giả lập."""
    return {"some_config_key": "some_value"}


@pytest.fixture
def chatbot_core(mock_config):
    """Fixture khởi tạo ChatbotCore với config giả lập."""
    return ChatbotCore(mock_config)


@pytest.mark.asyncio
async def test_process_request_success(chatbot_core):
    """Kiểm tra process_request trả về kết quả thành công."""
    # Mock graph.ainvoke trả về kết quả hợp lệ
    mock_result = {
        "messages": [
            MagicMock(content="The budget for IT is 10 million.")
        ]
    }
    with patch.object(chatbot_graph, "ainvoke", new=AsyncMock(return_value=mock_result)):
        result = await chatbot_core.process_request("What’s the budget for IT?", "user123")

        # <PERSON><PERSON><PERSON> tra kết quả
        assert result == "The budget for IT is 10 million."
        assert chatbot_graph.ainvoke.called
        call_args = chatbot_graph.ainvoke.call_args[0]
        assert call_args[0]["messages"][0] == ("user", "What’s the budget for IT?")
        assert call_args[1]["configurable"]["user_id"] == "user123"


@pytest.mark.asyncio
async def test_process_request_no_content(chatbot_core):
    """Kiểm tra process_request khi message không có content."""
    # Mock graph.ainvoke trả về message không có content
    mock_message = MagicMock()
    delattr(mock_message, "content")  # Xóa thuộc tính content
    mock_message.__str__.return_value = "Fallback response"
    mock_result = {"messages": [mock_message]}

    with patch.object(chatbot_graph, "ainvoke", new=AsyncMock(return_value=mock_result)):
        result = await chatbot_core.process_request("Invalid question", "user123")

        # Kiểm tra kết quả fallback
        assert result == "Fallback response"


@pytest.mark.asyncio
async def test_process_request_error(chatbot_core, caplog):
    """Kiểm tra process_request khi gặp lỗi."""
    # Mock graph.ainvoke ném exception
    with patch.object(chatbot_graph, "ainvoke", new=AsyncMock(side_effect=Exception("Graph error"))):
        with caplog.at_level(logging.ERROR):
            result = await chatbot_core.process_request("What’s the budget?", "user123")

            # Kiểm tra kết quả lỗi
            assert result == "Sorry, I encountered an error processing your request."
            assert "Error processing request: Graph error" in caplog.text


@pytest.mark.asyncio
async def test_process_request_empty_message(chatbot_core):
    """Kiểm tra process_request với message rỗng."""
    mock_result = {
        "messages": [
            MagicMock(content="Please provide a valid question.")
        ]
    }
    with patch.object(chatbot_graph, "ainvoke", new=AsyncMock(return_value=mock_result)):
        result = await chatbot_core.process_request("", "user123")

        # Kiểm tra kết quả
        assert result == "Please provide a valid question."
