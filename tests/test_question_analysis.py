#!/usr/bin/env python3
"""
Test script to extract questions from main.py and analyze them using the analyze_question function.

This script:
1. Extracts all questions from the SAMPLE_QUESTIONS list in main.py
2. Passes each question through the analyze_question function from planning.py
3. Displays the JSON output/result for each question processed
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph.planning import QuestionAnalyzer
from src.llm_providers.llm_provider import LLMProvider

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Hardcoded backup questions (in case extraction fails)
SAMPLE_QUESTIONS = [
    "So sánh tình hình đi muộn trong tháng 3 của <PERSON>ễn T<PERSON>ế<PERSON>h với tình hình đi muộn trong tháng 5 của <PERSON>",
    "Thống kê tình hình đi muộn của Ngô Gia Phong trong tháng 3 và số lần nghỉ phép của Nguyễn Tiến Anh trong tháng 5",
    "Công ty có bao nhiêu người",
    "Danh sách các phòng ban trong công ty",
    "Ban giám đốc gồm những ai",
    "Có bao nhiêu nhân viên full-time ở D2",
    "Chi tiết về hồ sơ nhân sự của Nguyễn Tiến Anh",
    "Chi tiết về hồ sơ nhân sự của Ngô Ngọc Cường",
    "Tôi muốn biết chi tiết thông tin của Nguyễn Tiến Anh, Ngô Gia Phong",
    "Chi tiết về hồ sơ nhân viên trong phòng nhân sự",
    "Chi tiết về hồ sơ nhân viên trong D2",
    "Thống kê đi muộn của D2 từ đầu năm đến giờ",
    "Thống kê đi muộn của D2 từ đầu năm đến tháng 7/2025",
    "Nguyễn Công Trí đã làm ở công ty được mấy năm",
    "Nguyễn Tiến Anh và Ngô Gia Phong đã làm việc ở công ty được mấy năm?",
    "Tuần trước Nguyễn Tiến Anh có đi muộn không?",
    "Nguyễn Tiến Anh đi muộn bao nhiêu lần trong tháng 3?",
    "Thống kê đi muộn phòng nhân sự từ đầu năm đến giờ",
    "Nguyễn Tiến Anh còn mấy ngày nghỉ phép",
    "Số phép tồn của Ngô Gia Phong",
    "Thống kê nghỉ phép của Nguyễn Tiến Anh từ đầu năm đến giờ?",
    "Thống kê nghỉ phép của Nguyễn Công Trí từ đầu năm đến giờ?",
    "thống kê nghỉ phép từ đầu nằm đến giờ phòng nhân sự",
    "Thống kê nghỉ phép phòng nhân sự 2024",
    "Tháng 1/2025 Ngô Gia Phong đi muộn mấy lần",
    "Thống kê đi muộn của D2 tuần trước",
    "Thống kê đi muộn của D2 từ 02/06/2025 đến 08/06/2025",
    "Thống kê đi muộn của phòng nhân sự tháng này",
    "Thống kê đi muộn tháng 1, tháng 2 của Ngô Gia Phong",
    "So sánh đi muộn phòng nhân sự tháng này với tháng trước"
]


class MockLLM:
    """Mock LLM provider for testing purposes"""

    async def process_message(self, message: str, context: Dict, prompt: str) -> str:
        """
        Mock implementation that returns a sample JSON response based on the question type.
        In a real scenario, this would call an actual LLM.
        """
        # Simple heuristic-based mock responses for testing
        message_lower = message.lower()

        if "công ty có bao nhiêu" in message_lower:
            return '''[{
                "intent": "statistics",
                "scope": {"type": "company", "value": "all"},
                "entities": {"metric": "count"}
            }]'''
        elif "phòng ban" in message_lower and "danh sách" in message_lower:
            return '''[{
                "intent": "statistics", 
                "scope": {"type": "company", "value": "all"},
                "entities": {"metric": "department_list"}
            }]'''
        elif "đi muộn" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô gia phong", "nguyễn công trí"]):
                # Extract name (simplified)
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
                return f'''[{{
                    "intent": "timesheet",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"time_period": "tháng 3"}}
                }}]'''
            elif any(dept in message_lower for dept in ["d2", "phòng nhân sự"]):
                dept_name = "Phòng Sản Xuất D2" if "d2" in message_lower else "Phòng nhân sự"
                return f'''[{{
                    "intent": "timesheet",
                    "scope": {{"type": "department", "value": "{dept_name}"}},
                    "entities": {{"time_period": "từ đầu năm đến giờ"}}
                }}]'''
        elif "nghỉ phép" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô gia phong", "nguyễn công trí"]):
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
                return f'''[{{
                    "intent": "leave",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"time_period": "từ đầu năm đến giờ"}}
                }}]'''
        elif "phép tồn" in message_lower or "còn mấy ngày nghỉ phép" in message_lower:
            name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Gia Phong"
            return f'''[{{
                "intent": "remain_leave",
                "scope": {{"type": "individual", "value": "{name}"}},
                "entities": {{}}
            }}]'''
        elif "hồ sơ" in message_lower:
            if any(name in message_lower for name in ["nguyễn tiến anh", "ngô ngọc cường"]):
                name = "Nguyễn Tiến Anh" if "nguyễn tiến anh" in message_lower else "Ngô Ngọc Cường"
                return f'''[{{
                    "intent": "statistics",
                    "scope": {{"type": "individual", "value": "{name}"}},
                    "entities": {{"metric": "list_details"}}
                }}]'''
            elif "phòng nhân sự" in message_lower:
                return '''[{
                    "intent": "statistics",
                    "scope": {"type": "department", "value": "Phòng nhân sự"},
                    "entities": {"metric": "list_details"}
                }]'''

        # Default fallback response
        return '''[{
            "intent": "casual",
            "scope": {"type": "company", "value": ""},
            "entities": {"error": "Unable to analyze question"}
        }]'''


def create_mock_sub_agents() -> Dict[str, Dict[str, Any]]:
    """Create mock sub_agents structure for testing"""
    return {
        "attendance": {
            "timesheet": {
                "description": "Handles queries about employee attendance, including late check-ins, early check-outs, absences, and leave days."
            }
        },
        "payroll": {
            "payroll": {
                "description": "Handles queries about employee salary and compensation."
            }
        },
        "leave": {
            "leave": {
                "description": "Handles queries about employee leave days and vacation time."
            }
        },
        "performance": {
            "performance": {
                "description": "Handles queries about employee performance metrics and evaluations."
            }
        }
    }


def create_test_state(question: str) -> Dict[str, Any]:
    """Create a test state dictionary for the analyze_question function"""
    return {
        "message": question,
        "context": {
            "chat_history": []
        }
    }


async def test_question_analysis(use_real_llm: bool = False, limit: int = None):
    """Main test function that processes all sample questions"""
    # Extract questions from main.py
    questions = SAMPLE_QUESTIONS

    # Limit questions if specified
    if limit:
        questions = questions[:limit]

    print("=" * 80)
    print("QUESTION ANALYSIS TEST")
    print("=" * 80)
    print(f"Testing {len(questions)} questions from main.py")
    if use_real_llm:
        print("Using REAL LLM for analysis")
    else:
        print("Using MOCK LLM for analysis")
    print("=" * 80)

    # Initialize components
    if use_real_llm:
        try:
            # Try to use real LLM provider
            real_llm = LLMProvider(provider={"paid": "openai", "free": "gemini"})
            llm = real_llm
            print("✅ Real LLM provider initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize real LLM provider: {e}")
            print("Falling back to mock LLM...")
            llm = MockLLM()
    else:
        llm = MockLLM()

    mock_sub_agents = create_mock_sub_agents()

    # Create QuestionAnalyzer instance
    analyzer = QuestionAnalyzer(llm=llm, sub_agents=mock_sub_agents)

    results = []
    successful_analyses = 0
    failed_analyses = 0

    for i, question in enumerate(questions, 1):
        print(f"\n[{i:2d}/{len(SAMPLE_QUESTIONS)}] Processing question:")
        print(f"Question: {question}")
        print("-" * 60)

        try:
            # Create state for this question
            state = create_test_state(question)

            # Analyze the question
            analysis_result = await analyzer.analyze_question(state)

            # Pretty print the JSON result
            formatted_result = json.dumps(analysis_result, indent=2, ensure_ascii=False)
            print("Analysis Result:")
            print(formatted_result)

            results.append({
                "question": question,
                "analysis": analysis_result,
                "status": "success"
            })
            successful_analyses += 1

        except Exception as e:
            error_msg = f"Error analyzing question: {str(e)}"
            print(f"❌ {error_msg}")
            logger.error(f"Failed to analyze question '{question}': {e}")

            results.append({
                "question": question,
                "analysis": None,
                "status": "error",
                "error": str(e)
            })
            failed_analyses += 1

        print("=" * 60)

    # Print summary
    print(f"\n{'=' * 80}")
    print("SUMMARY")
    print(f"{'=' * 80}")
    print(f"Total questions processed: {len(questions)}")
    print(f"Successful analyses: {successful_analyses}")
    print(f"Failed analyses: {failed_analyses}")
    print(f"Success rate: {(successful_analyses / len(questions) * 100):.1f}%")

    # Save results to file
    output_file = "question_analysis_results.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\nResults saved to: {output_file}")
    except Exception as e:
        logger.error(f"Failed to save results to file: {e}")

    return results


if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test question analysis with mock or real LLM')
    parser.add_argument('--real-llm', action='store_true',
                        help='Use real LLM instead of mock (requires API keys)')
    parser.add_argument('--limit', type=int, default=None,
                        help='Limit number of questions to test (for quick testing)')
    args = parser.parse_args()

    try:
        # Note: question limiting is now handled inside test_question_analysis function
        # by modifying the extracted questions list

        # Run the test
        results = asyncio.run(test_question_analysis(use_real_llm=args.real_llm, limit=args.limit))

        # Exit with appropriate code
        failed_count = sum(1 for r in results if r["status"] == "error")
        sys.exit(0 if failed_count == 0 else 1)

    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during testing: {e}")
        sys.exit(1)
