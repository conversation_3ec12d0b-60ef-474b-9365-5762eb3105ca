import asyncio
import json
import logging
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, TypedDict, Any, Tu<PERSON>, Union
from enum import Enum
from pydantic import BaseModel, Field

from dotenv import load_dotenv
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint import MemorySaver

# Import your existing modules
from src.modules.router.subdomain_router import SubdomainRouter
from src.agents.agent_internal.tardiness_agent.tardiness_agent import TardinessAgent
from src.agents.agent_internal.timekeeping_agent.timekeeping_agent import TimekeepingAgent
from src.utils.utils import validate_llm_response, format_json_response

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()
SYSTEM_PROMPT = os.getenv("SYSTEM_PROMPT")
PLANNER_PROMPT = os.getenv("PLANNER_PROMPT", "You are an intelligent planning agent for an HR chatbot system.")
REFLECTION_PROMPT = os.getenv("REFLECTION_PROMPT", "Analyze the execution and result to improve response quality.")

class AgentType(str, Enum):
    PLANNER = "planner"
    EXECUTOR = "executor"
    ROUTER = "router"
    TIME_EXTRACTOR = "time_extractor"
    EMPLOYEE_FINDER = "employee_finder"
    REFLECTION = "reflection"
    CASUAL = "casual"
    HUMAN_ASSISTANCE = "human_assistance"

class ExecutionStatus(str, Enum):
    PLANNING = "planning"
    ROUTING = "routing"
    EXECUTING = "executing"
    REFLECTING = "reflecting"
    COMPLETED = "completed"
    ERROR = "error"
    NEEDS_HUMAN_HELP = "needs_human_help"

class Plan(BaseModel):
    steps: List[str] = Field(default_factory=list)
    current_step: int = 0
    requires_employee_info: bool = False
    requires_time_extraction: bool = False
    requires_external_api: bool = False
    api_endpoints: List[str] = Field(default_factory=list)
    complex_query: bool = False
    concurrent_steps: List[List[str]] = Field(default_factory=list)
    
    def get_current_step(self) -> Optional[str]:
        if 0 <= self.current_step < len(self.steps):
            return self.steps[self.current_step]
        return None
    
    def advance(self) -> None:
        self.current_step += 1
    
    def is_completed(self) -> bool:
        return self.current_step >= len(self.steps)

class ChatbotState(TypedDict):
    # User and message information
    user_id: str
    message: str
    domain: str
    
    # Context and state tracking
    context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    execution_status: ExecutionStatus
    current_agent: AgentType
    previous_agents: List[AgentType]
    
    # Planning and execution
    plan: Optional[Plan]
    execution_steps: List[Dict[str, Any]]
    execution_results: Dict[str, Any]
    parallel_tasks_results: Dict[str, Any]
    
    # Routing and domain information
    subdomain: str
    entities: Dict[str, Any]
    
    # Agent state
    agent: Optional[Any]
    agent_name: str
    
    # Response and results
    response: Optional[str]
    intermediate_responses: List[str]
    api_result: Optional[Dict[str, Any]]
    employee_id: Optional[str]
    
    # Error handling
    error_message: Optional[str]
    retry_count: int
    human_feedback: Optional[str]
    
    # Reflection data
    reflection_notes: List[str]
    confidence_score: float

class ChatbotGraph:
    def __init__(self, config, domain: str, llm, domains_dir: str, function_caller, context_manager):
        self.llm = llm
        self.function_caller = function_caller
        self.context_manager = context_manager
        self.timeout = config.timeout
        self.domain = domain
        self.domains_dir = domains_dir
        self.subdomain_router = SubdomainRouter(self.llm, domains_dir)
        self.agents = self._load_agents()
        
        # Create persistent memory with MemorySaver
        self.memory_saver = MemorySaver()
        
        # Load tools and APIs
        self.subdomains = ["attendance", "payroll", "leave", "performance"]  # Expanded subdomains
        self.tools = self.get_list_tools_domain(self.subdomains)
        self.employee_tools = self._load_employee_tools()
        
        # Build the graph
        self.graph = self._build_graph()
        
        # Setup human-in-the-loop configuration
        self.human_in_loop_threshold = config.human_in_loop_threshold
        self.max_retries = config.max_retries
        
    def _load_agents(self) -> Dict[str, Dict[str, object]]:
        """Load all domain-specific agents"""
        agents = {
            "attendance": {
                "tardiness": TardinessAgent(self.function_caller, self.llm),
                "timekeeping": TimekeepingAgent(self.function_caller, self.llm)
            }
            # Add more domains and agents as needed
        }
        return agents
    
    def _load_employee_tools(self) -> Dict:
        """Load employee-related tools"""
        employee_tools = {}
        employee_api_path = os.path.join(self.domains_dir, self.domain, "common", "apis", "employee_list_post.json")
        
        try:
            with open(employee_api_path, 'r') as file:
                employee_tools["get_employee_id"] = json.load(file)
        except FileNotFoundError:
            # Fallback to your current path
            with open('/home/<USER>/Documents/AI/HRBL_AI/chatbot-rbl/src/domains/hr/attendance/apis/employee_list_post.json', 'r') as file:
                employee_tools["get_employee_id"] = json.load(file)
                
        return employee_tools
    
    def _build_graph(self) -> StateGraph:
        """Build the enhanced LangGraph with advanced features"""
        graph = StateGraph(ChatbotState)
        
        # Core nodes
        graph.add_node("initialize", self._initialize)
        graph.add_node("check_history", self._check_history)
        graph.add_node("handle_casual", self._handle_casual)
        graph.add_node("create_plan", self._create_plan)
        graph.add_node("get_employee_id", self._get_employee_id)
        graph.add_node("extract_time", self._extract_time)
        graph.add_node("route_to_subdomain", self._route_to_subdomain)
        graph.add_node("select_agent", self._select_agent)
        graph.add_node("execute_agent", self._execute_agent)
        graph.add_node("execute_parallel_tasks", self._execute_parallel_tasks)
        graph.add_node("reflection", self._reflection)
        graph.add_node("human_assistance", self._human_assistance)
        graph.add_node("update_context", self._update_context)
        graph.add_node("generate_response", self._generate_response)
        graph.add_node("fallback", self._fallback)
        
        # Use ToolNode for API calls
        graph.add_node("api_tool", ToolNode(self._execute_api_call))
        
        # Set entry point
        graph.set_entry_point("initialize")
        
        # Main flow
        graph.add_conditional_edges(
            "initialize",
            lambda state: "check_history"
        )
        
        graph.add_conditional_edges(
            "check_history",
            lambda state: "handle_casual" if state.get("current_agent") == AgentType.CASUAL 
                     else "create_plan"
        )
        
        graph.add_edge("handle_casual", "update_context")
        
        graph.add_conditional_edges(
            "create_plan",
            lambda state: self._plan_router(state)
        )
        
        # Employee and time extraction can happen in parallel
        graph.add_edge("get_employee_id", "route_to_subdomain")
        graph.add_edge("extract_time", "route_to_subdomain")
        
        graph.add_conditional_edges(
            "route_to_subdomain",
            lambda state: "fallback" if state.get("error_message") else "select_agent"
        )
        
        graph.add_edge("select_agent", "execute_agent")
        
        graph.add_conditional_edges(
            "execute_agent",
            lambda state: "reflection" if not state.get("error_message") 
                     else "human_assistance" if state.get("retry_count") >= self.max_retries 
                     else "execute_agent"  # Retry logic
        )
        
        graph.add_conditional_edges(
            "reflection",
            lambda state: "generate_response" if state.get("confidence_score", 0) > 0.7
                     else "human_assistance" if state.get("confidence_score", 0) < 0.3
                     else "execute_agent"  # Retry with insights
        )
        
        graph.add_edge("human_assistance", "generate_response")
        graph.add_edge("generate_response", "update_context")
        graph.add_edge("fallback", "update_context")
        graph.add_edge("update_context", END)
        
        # Register the memory saver
        return graph.compile(checkpointer=self.memory_saver)
    
    async def _initialize(self, state: ChatbotState) -> ChatbotState:
        """Initialize the state with default values"""
        # Keep any existing values, but ensure all required fields exist
        state.setdefault("execution_status", ExecutionStatus.PLANNING)
        state.setdefault("current_agent", AgentType.PLANNER)
        state.setdefault("previous_agents", [])
        state.setdefault("plan", None)
        state.setdefault("execution_steps", [])
        state.setdefault("execution_results", {})
        state.setdefault("parallel_tasks_results", {})
        state.setdefault("intermediate_responses", [])
        state.setdefault("retry_count", 0)
        state.setdefault("reflection_notes", [])
        state.setdefault("confidence_score", 1.0)
        state.setdefault("entities", {})
        state.setdefault("agent_name", "")
        
        # Setup conversation history
        state.setdefault("conversation_history", [])
        if state["context"].get("short_term_history"):
            state["conversation_history"].extend(state["context"]["short_term_history"])
        if state["context"].get("long_term_history"):
            state["conversation_history"].extend(state["context"]["long_term_history"])
            
        logger.info(f"Initialized state for user {state['user_id']}")
        return state
    
    async def _check_history(self, state: ChatbotState) -> ChatbotState:
        """Enhanced history checking with better context retrieval"""
        # First, check if it's a casual or unrelated question
        casual_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "Instructions:\n"
            "1. Determine if the question is a casual greeting (e.g., 'xin chào', 'hello') or unrelated to internal company data (e.g., 'how's the weather?', 'tell me a joke').\n"
            "2. If it is casual or unrelated, respond with exactly 'CASUAL' (nothing else).\n"
            "3. If it relates to internal company data (e.g., tardiness, timesheet, employee info), respond with exactly 'DATA_QUERY' (nothing else).\n"
            "Be very precise in your classification."
        )

        try:
            casual_response = await self.llm.process_message(state["message"], state["context"], casual_prompt)
            
            if isinstance(casual_response, str) and "CASUAL" in casual_response.strip().upper():
                state["current_agent"] = AgentType.CASUAL
                logger.info(f"Classified as casual interaction for user {state['user_id']}")
                return state
        except Exception as e:
            logger.error(f"Error checking casual intent: {str(e)}")
        
        # Check for exact match in history
        existing_entry = self.context_manager.check_existing_question(state["user_id"], state["message"])
        if existing_entry:
            logger.info(f"Found exact match in history for user {state['user_id']}")
            state["response"] = existing_entry["response"]
            state["execution_status"] = ExecutionStatus.COMPLETED
            return state
        
        # Use semantic search to find similar questions (enhanced from original)
        similar_entries = self._semantic_search_history(state["user_id"], state["message"], threshold=0.85)
        if similar_entries:
            most_similar = similar_entries[0]
            if most_similar["similarity"] > 0.92:  # High confidence threshold
                logger.info(f"Found very similar question in history for user {state['user_id']}")
                state["response"] = most_similar["entry"]["response"]
                state["execution_status"] = ExecutionStatus.COMPLETED
                return state
            
            # Add as context but continue with planning
            state["context"]["similar_questions"] = [
                {"question": entry["entry"]["message"], 
                 "answer": entry["entry"]["response"],
                 "similarity": entry["similarity"]} 
                for entry in similar_entries[:3]
            ]
        
        # Continue to planning stage
        return state
    
    def _semantic_search_history(self, user_id: str, query: str, threshold: float = 0.8) -> List[Dict]:
        """Semantic search across user history (placeholder - implement with embeddings)"""
        # This is a placeholder - in production, implement with vector embeddings
        # For now, just simulate with exact substring matching
        results = []
        history = self.context_manager.get_context(user_id).get("short_term_history", []) + \
                 self.context_manager.get_context(user_id).get("long_term_history", [])
        
        for entry in history:
            # Simplistic matching - replace with vector similarity in production
            if query.lower() in entry["message"].lower():
                similarity = 0.9  # Simulated similarity score
                results.append({"entry": entry, "similarity": similarity})
                
        return sorted(results, key=lambda x: x["similarity"], reverse=True)
    
    async def _handle_casual(self, state: ChatbotState) -> ChatbotState:
        """Handle casual interactions with a friendly conversational tone"""
        casual_response_prompt = (
            f"{SYSTEM_PROMPT}\n\n"
            f"Current question: '{state['message']}'\n\n"
            "This has been classified as a casual interaction. Provide a friendly, conversational response.\n"
            "If the user greeted in Vietnamese, respond in Vietnamese. Otherwise respond in English.\n"
            "Keep it brief, friendly and natural."
        )
        
        try:
            response = await self.llm.process_message(state["message"], state["context"], casual_response_prompt)
            state["response"] = response.strip() if isinstance(response, str) else "I'm here to help with HR questions."
            state["execution_status"] = ExecutionStatus.COMPLETED
            logger.info(f"Handled casual interaction for user {state['user_id']}")
        except Exception as e:
            logger.error(f"Error handling casual interaction: {str(e)}")
            state["response"] = "I'm here to help with HR questions. How can I assist you today?"
            state["execution_status"] = ExecutionStatus.COMPLETED
            
        return state
    
    async def _create_plan(self, state: ChatbotState) -> ChatbotState:
        """Generate an execution plan for the query"""
        plan_prompt = (
            f"{PLANNER_PROMPT}\n\n"
            f"User query: '{state['message']}'\n\n"
            f"Available subdomains: {', '.join(self.subdomains)}\n"
            "Available API categories: employee information, time tracking, attendance, leave management\n\n"
            "Instructions:\n"
            "1. Analyze the query to understand what the user is asking about.\n"
            "2. Create a step-by-step plan to answer the query.\n"
            "3. Return your response as a valid JSON object with the following structure:\n"
            "{\n"
            "  \"steps\": [\"step1\", \"step2\", ...],\n"
            "  \"requires_employee_info\": boolean,\n"
            "  \"requires_time_extraction\": boolean,\n"
            "  \"requires_external_api\": boolean,\n"
            "  \"api_endpoints\": [\"endpoint1\", ...],\n"
            "  \"complex_query\": boolean,\n"
            "  \"concurrent_steps\": [[\"step1\", \"step2\"], [\"step3\"]]\n"
            "}\n\n"
            "Example steps: check_history, get_employee_details, extract_time_period, query_attendance_data, etc.\n"
            "If steps can be executed in parallel, group them in the concurrent_steps array."
        )
        
        try:
            response = await self.llm.process_message(state["message"], state["context"], plan_prompt)
            cleaned_response = response.strip()
            
            # Extract the JSON part if surrounded by backticks
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()
                
            # Ensure proper JSON format
            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"
                
            # Parse the plan
            plan_dict = json.loads(cleaned_response)
            state["plan"] = Plan(**plan_dict)
            state["execution_status"] = ExecutionStatus.ROUTING
            
            logger.info(f"Created execution plan for user {state['user_id']}")
            
            # Add to execution steps for tracking
            state["execution_steps"].append({
                "step": "create_plan",
                "timestamp": datetime.now().isoformat(),
                "result": "Plan created successfully"
            })
            
        except Exception as e:
            logger.error(f"Error creating plan: {str(e)}")
            # Create a default plan
            state["plan"] = Plan(
                steps=["get_employee_id", "extract_time", "route_to_subdomain", "execute_agent"],
                requires_employee_info=True,
                requires_time_extraction=True
            )
            state["execution_steps"].append({
                "step": "create_plan",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error creating plan, using default: {str(e)}"
            })
            
        return state
    
    def _plan_router(self, state: ChatbotState) -> str:
        """Route to the next step based on the plan"""
        if not state["plan"]:
            return "fallback"
        
        # Check if we have parallel steps to execute
        if state["plan"].concurrent_steps:
            next_parallel_group = state["plan"].concurrent_steps[0]
            state["plan"].concurrent_steps = state["plan"].concurrent_steps[1:]
            state["execution_steps"].append({
                "step": "parallel_execution",
                "timestamp": datetime.now().isoformat(),
                "tasks": next_parallel_group
            })
            # Set up for parallel execution
            state["parallel_tasks"] = next_parallel_group
            return "execute_parallel_tasks"
        
        # Otherwise, follow sequential steps
        current_step = state["plan"].get_current_step()
        if not current_step:
            return "reflection"  # All steps completed
        
        state["plan"].advance()  # Move to next step for future
        
        # Map plan steps to actual nodes
        step_mapping = {
            "get_employee_id": "get_employee_id",
            "extract_time": "extract_time",
            "route_to_subdomain": "route_to_subdomain",
            "select_agent": "select_agent",
            "execute_agent": "execute_agent",
            "reflection": "reflection",
            "fallback": "fallback"
        }
        
        return step_mapping.get(current_step, "fallback")
    
    
    async def _execute_parallel_tasks(self, state: ChatbotState) -> ChatbotState:
        """Execute multiple tasks in parallel"""
        if not state.get("parallel_tasks"):
            return state
            
        tasks = state["parallel_tasks"]
        coroutines = []
        
        task_mapping = {
            "get_employee_id": self._get_employee_id,
            "extract_time": self._extract_time,
            # Add other tasks that can be parallelized
        }
        
        # Create a copy of the state for each parallel task
        for task in tasks:
            if task in task_mapping:
                task_fn = task_mapping[task]
                task_state = state.copy()
                coroutines.append(task_fn(task_state))
        
        # Execute all tasks in parallel
        if coroutines:
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            
            # Merge results back into state
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Parallel task error: {str(result)}")
                    continue
                    
                task = tasks[i]
                # Merge relevant fields based on the task
                if task == "get_employee_id" and result.get("employee_id"):
                    state["employee_id"] = result["employee_id"]
                elif task == "extract_time" and result.get("entities", {}).get("time_period"):
                    state["entities"]["time_period"] = result["entities"]["time_period"]
                    
                # Store all results
                state["parallel_tasks_results"][task] = result
        
        # Return to the plan router for next steps
        return state
    
    async def _get_employee_id(self, state: ChatbotState) -> ChatbotState:
        """Enhanced employee identification with better error handling"""
        prompt = (
            "Extract the employee name from the user query. Look for names or references to specific employees.\n\n"
            f"User query: '{state['message']}'\n\n"
            "Instructions:\n"
            "1. First determine if the query mentions or refers to a specific employee. Look for:\n"
            "   - Direct names (e.g., 'Phong', 'Minh', 'Nguyen Van A')\n"
            "   - Pronouns or references ('his attendance', 'her timesheet') if there's context\n"
            "   - Implicit references that clearly point to a specific employee\n"
            "2. If you find an employee reference, extract the name or identifier\n"
            "3. If no employee is referenced, respond with 'NO_EMPLOYEE'\n"
            "4. If there's ambiguity, respond with 'AMBIGUOUS_EMPLOYEE'\n"
            "5. Your response should be ONLY the employee name or one of the special codes."
        )
        
        try:
            # First check if we need employee info according to the plan
            if state.get("plan") and not state["plan"].requires_employee_info:
                logger.info("Skipping employee identification as per plan")
                return state
                
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            employee_reference = response.strip() if isinstance(response, str) else "NO_EMPLOYEE"
            
            # Handle the special cases
            if employee_reference == "NO_EMPLOYEE":
                logger.info("No employee reference found in query")
                return state
                
            if employee_reference == "AMBIGUOUS_EMPLOYEE":
                state["error_message"] = "The employee reference is ambiguous. Please provide a specific name."
                return state
                
            # Employee reference found, call API to get ID
            employee_tool = self.employee_tools.get("get_employee_id")
            if not employee_tool:
                logger.error("Employee search tool not available")
                return state
                
            # Create search parameters
            search_params = {"search": employee_reference}
            function_data = {
                "path": employee_tool['function']['path'],
                "method": employee_tool['function']['method'],
                "params": search_params
            }
            
            # Call the employee search API
            result = await self.function_caller.execute(function_data)
            
            if not result.get('data') or len(result.get('data')) == 0:
                state["error_message"] = f"No employee found matching '{employee_reference}'. Please check the name and try again."
                return state
                
            if len(result['data']) > 1:
                # Multiple matches, provide disambiguation
                employee_list = "\n".join([f"- {emp.get('name', 'Unknown')}" for emp in result['data'][:5]])
                state["error_message"] = (
                    f"I found multiple employees matching '{employee_reference}':\n{employee_list}\n\n"
                    f"Please provide a more specific name."
                )
                return state
                
            # Success - single employee found
            state["employee_id"] = result['data'][0]['id']
            state["entities"]["employee"] = {
                "id": result['data'][0]['id'],
                "name": result['data'][0]['name'],
                "department": result['data'][0].get('department', 'Unknown'),
                "position": result['data'][0].get('position', 'Unknown')
            }
            
            logger.info(f"Found employee ID for '{employee_reference}': {state['employee_id']}")
            
            # Add to execution steps
            state["execution_steps"].append({
                "step": "get_employee_id",
                "timestamp": datetime.now().isoformat(),
                "result": f"Found employee: {result['data'][0]['name']}"
            })
            
        except Exception as e:
            logger.error(f"Error in employee identification: {str(e)}")
            state["error_message"] = "Could not process employee information. Please try again."
            state["execution_steps"].append({
                "step": "get_employee_id",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _extract_time(self, state: ChatbotState) -> ChatbotState:
        """Enhanced time period extraction with better recognition of Vietnamese time references"""
        # First check if we need time info according to the plan
        if state.get("plan") and not state["plan"].requires_time_extraction:
            logger.info("Skipping time extraction as per plan")
            return state
            
        current_date = datetime.now().strftime("%Y-%m-%d")
        prompt = (
            f"Extract time period from query, handling both English and Vietnamese time references.\n\n"
            f"User query: '{state['message']}'\n"
            f"Current date: {current_date}\n\n"
            "Instructions:\n"
            "1. Identify any time references in the query (e.g., 'yesterday', 'last month', 'March 2025', 'tuần trước', 'tháng này').\n"
            "2. Pay special attention to Vietnamese time references:\n"
            "   - 'hôm qua' = yesterday\n"
            "   - 'tuần trước/tuần vừa rồi' = last week\n"
            "   - 'tháng này' = this month\n"
            "   - 'tháng trước/tháng vừa rồi' = last month\n"
            "3. Convert to precise date range in 'YYYY-MM-DD' format:\n"
            "   - For a single day, use same date for start and end\n"
            "   - For a week, use Monday to Sunday\n"
            "   - For a month, use first day to last day\n"
            "4. If no time is specified, default to current month\n"
            "5. Return ONLY a valid JSON with format: {\"start_date\": \"YYYY-MM-DD\", \"end_date\": \"YYYY-MM-DD\"}"
        )

        try:
            response = await self.llm.process_message(state["message"], state["context"], prompt)
            cleaned = response.strip()
            
            # Extract JSON from response if needed
            if "```json" in cleaned:
                cleaned = cleaned.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned:
                cleaned = cleaned.split("```")[1].strip()
                
            # Ensure proper JSON format with double quotes
            cleaned = cleaned.replace("'", '"')
            if not cleaned.startswith("{"):
                cleaned = "{" + cleaned.split("{", 1)[1]
            if not cleaned.endswith("}"):
                cleaned = cleaned.split("}", 1)[0] + "}"

            time_period = json.loads(cleaned)
            state["entities"]["time_period"] = time_period
            
            # Add to execution steps
            state["execution_steps"].append({
                "step": "extract_time",
                "timestamp": datetime.now().isoformat(),
                "result": f"Extracted time period: {time_period['start_date']} to {time_period['end_date']}"
            })
            
        except Exception as e:
            logger.error(f"Error extracting time: {str(e)}")
            # Default to current month on failure
            today = datetime.now()
            start_date = today.replace(day=1).strftime("%Y-%m-%d")
            end_date = (today.replace(day=1) + timedelta(days=31)).replace(day=1) - timedelta(days=1)
            state["entities"]["time_period"] = {"start_date": start_date, "end_date": end_date.strftime("%Y-%m-%d")}
            
            state["execution_steps"].append({
                "step": "extract_time",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}, defaulting to current month"
            })
            
        return state
    
    async def _route_to_subdomain(self, state: ChatbotState) -> ChatbotState:
        """Route the query to the appropriate subdomain"""
        try:
            subdomain = self.subdomain_router.route(state["message"], state["context"])
            state["subdomain"] = subdomain
            state["execution_steps"].append({
                "step": "route_to_subdomain",
                "timestamp": datetime.now().isoformat(),
                "result": f"Routed to subdomain: {subdomain}"
            })
        except Exception as e:
            logger.error(f"Error routing to subdomain: {str(e)}")
            state["error_message"] = "Could not determine the appropriate subdomain. Please try again."
            state["execution_steps"].append({
                "step": "route_to_subdomain",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _select_agent(self, state: ChatbotState) -> ChatbotState:
        """Select the appropriate agent for the subdomain"""
        try:
            subdomain_agents = self.agents.get(state["subdomain"], {})
            if not subdomain_agents:
                raise ValueError(f"No agents found for subdomain: {state['subdomain']}")
                
            # Select the agent based on the current step or context
            agent_name = state["plan"].get_current_step() if state["plan"] else "default"
            agent = subdomain_agents.get(agent_name)
            if not agent:
                raise ValueError(f"No agent found for step: {agent_name}")
                
            state["agent"] = agent
            state["agent_name"] = agent_name
            state["execution_steps"].append({
                "step": "select_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Selected agent: {agent_name}"
            })
        except Exception as e:
            logger.error(f"Error selecting agent: {str(e)}")
            state["error_message"] = "Could not select the appropriate agent. Please try again."
            state["execution_steps"].append({
                "step": "select_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _execute_agent(self, state: ChatbotState) -> ChatbotState:
        """Execute the selected agent"""
        try:
            if not state.get("agent"):
                raise ValueError("No agent selected for execution")
                
            agent = state["agent"]
            result = await agent.execute(state["message"], state["context"])
            state["execution_results"][state["agent_name"]] = result
            state["execution_steps"].append({
                "step": "execute_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Executed agent: {state['agent_name']}"
            })
        except Exception as e:
            logger.error(f"Error executing agent: {str(e)}")
            state["error_message"] = "Could not execute the selected agent. Please try again."
            state["execution_steps"].append({
                "step": "execute_agent",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _reflection(self, state: ChatbotState) -> ChatbotState:
        """Reflect on the execution and improve response quality"""
        try:
            reflection_prompt = (
                f"{REFLECTION_PROMPT}\n\n"
                f"Execution results: {json.dumps(state['execution_results'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Analyze the execution results and identify any issues or areas for improvement.\n"
                "2. Provide a confidence score between 0 and 1 for the overall response quality.\n"
                "3. If the confidence score is below 0.7, suggest improvements or additional steps.\n"
                "4. Return your response as a valid JSON object with the following structure:\n"
                "{\n"
                "  \"confidence_score\": float,\n"
                "  \"reflection_notes\": [\"note1\", \"note2\", ...],\n"
                "  \"suggested_steps\": [\"step1\", \"step2\", ...]\n"
                "}"
            )
            
            response = await self.llm.process_message(state["message"], state["context"], reflection_prompt)
            cleaned_response = response.strip()
            
            # Extract the JSON part if surrounded by backticks
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()
                
            # Ensure proper JSON format
            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"
                
            # Parse the reflection
            reflection_dict = json.loads(cleaned_response)
            state["confidence_score"] = reflection_dict.get("confidence_score", 1.0)
            state["reflection_notes"] = reflection_dict.get("reflection_notes", [])
            state["plan"].steps.extend(reflection_dict.get("suggested_steps", []))
            
            state["execution_steps"].append({
                "step": "reflection",
                "timestamp": datetime.now().isoformat(),
                "result": f"Reflection completed with confidence score: {state['confidence_score']}"
            })
            
        except Exception as e:
            logger.error(f"Error during reflection: {str(e)}")
            state["error_message"] = "Could not complete the reflection process. Please try again."
            state["execution_steps"].append({
                "step": "reflection",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _human_assistance(self, state: ChatbotState) -> ChatbotState:
        """Request human assistance for complex queries or low confidence responses"""
        try:
            human_assistance_prompt = (
                "The chatbot has encountered a complex query or low confidence response.\n\n"
                f"User query: '{state['message']}'\n\n"
                "Execution results:\n"
                f"{json.dumps(state['execution_results'], indent=2)}\n\n"
                "Reflection notes:\n"
                f"{json.dumps(state['reflection_notes'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Review the execution results and reflection notes.\n"
                "2. Provide a response to the user or suggest additional steps for the chatbot.\n"
                "3. Return your response as a valid JSON object with the following structure:\n"
                "{\n"
                "  \"response\": \"string\",\n"
                "  \"suggested_steps\": [\"step1\", \"step2\", ...]\n"
                "}"
            )
            
            response = await self.llm.process_message(state["message"], state["context"], human_assistance_prompt)
            cleaned_response = response.strip()
            
            # Extract the JSON part if surrounded by backticks
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()
                
            # Ensure proper JSON format
            if not cleaned_response.startswith("{"):
                cleaned_response = "{" + cleaned_response.split("{", 1)[1]
            if not cleaned_response.endswith("}"):
                cleaned_response = cleaned_response.split("}", 1)[0] + "}"
                
            # Parse the human assistance response
            human_assistance_dict = json.loads(cleaned_response)
            state["response"] = human_assistance_dict.get("response", "I'm here to help with HR questions. How can I assist you today?")
            state["plan"].steps.extend(human_assistance_dict.get("suggested_steps", []))
            
            state["execution_steps"].append({
                "step": "human_assistance",
                "timestamp": datetime.now().isoformat(),
                "result": "Human assistance provided"
            })
            
        except Exception as e:
            logger.error(f"Error during human assistance: {str(e)}")
            state["error_message"] = "Could not complete the human assistance process. Please try again."
            state["execution_steps"].append({
                "step": "human_assistance",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _update_context(self, state: ChatbotState) -> ChatbotState:
        """Update the context with the latest state information"""
        try:
            self.context_manager.update_context(state["user_id"], state["context"])
            state["execution_steps"].append({
                "step": "update_context",
                "timestamp": datetime.now().isoformat(),
                "result": "Context updated successfully"
            })
        except Exception as e:
            logger.error(f"Error updating context: {str(e)}")
            state["error_message"] = "Could not update the context. Please try again."
            state["execution_steps"].append({
                "step": "update_context",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _generate_response(self, state: ChatbotState) -> ChatbotState:
        """Generate the final response for the user"""
        try:
            response_prompt = (
                "Generate a final response for the user based on the execution results and context.\n\n"
                f"User query: '{state['message']}'\n\n"
                "Execution results:\n"
                f"{json.dumps(state['execution_results'], indent=2)}\n\n"
                "Instructions:\n"
                "1. Review the execution results and context.\n"
                "2. Generate a final response for the user.\n"
                "3. Return your response as a string."
            )
            
            response = await self.llm.process_message(state["message"], state["context"], response_prompt)
            state["response"] = response.strip() if isinstance(response, str) else "I'm here to help with HR questions. How can I assist you today?"
            state["execution_status"] = ExecutionStatus.COMPLETED
            
            state["execution_steps"].append({
                "step": "generate_response",
                "timestamp": datetime.now().isoformat(),
                "result": "Response generated successfully"
            })
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            state["error_message"] = "Could not generate the response. Please try again."
            state["execution_steps"].append({
                "step": "generate_response",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
    
    async def _fallback(self, state: ChatbotState) -> ChatbotState:
        """Fallback handler for errors or unhandled cases"""
        state["response"] = "I'm sorry, I couldn't process your request. Please try again later."
        state["execution_status"] = ExecutionStatus.ERROR
        state["execution_steps"].append({
            "step": "fallback",
            "timestamp": datetime.now().isoformat(),
            "result": "Fallback executed"
        })
        return state
    
    async def _execute_api_call(self, state: ChatbotState, tool_node: ToolNode) -> ChatbotState:
        """Execute an API call using the provided tool node"""
        try:
            result = await tool_node.execute(state["message"], state["context"])
            state["api_result"] = result
            state["execution_steps"].append({
                "step": "api_tool",
                "timestamp": datetime.now().isoformat(),
                "result": "API call executed successfully"
            })
        except Exception as e:
            logger.error(f"Error executing API call: {str(e)}")
            state["error_message"] = "Could not execute the API call. Please try again."
            state["execution_steps"].append({
                "step": "api_tool",
                "timestamp": datetime.now().isoformat(),
                "result": f"Error: {str(e)}"
            })
            
        return state
