name: Unit Test Suite
version: 0.0.1
schema: v1
prompts:
  - name: Unit Test Suite
    description: Create a comprehensive suite of unit tests
    prompt: |
      < system >
      You will be acting as a senior software engineer helping a colleague to create unit tests for their code.
      < /system >
      You will follow the guidelines to understand what unit testing is, and how to generate useful unit tests:
      {{{ url "https://aws.amazon.com/what-is/unit-testing/" }}}
      To better understand how we perform unit testing for this codebase, use the following examples:
      {{{ codebase "Find examples of unit testing" }}}
      Please write a thorough suite of unit tests for this code, making sure to cover all relevant edge cases.

