openapi: 3.0.0
info:
  title: Weekly AI
  version: 1.0.0
  description: 'API specification converted from Postman collection: Weekly AI'
servers:
- url: https://api.weekly.vn
  description: Production server
paths:
  /employee/list:
    post:
      summary: <PERSON><PERSON> sách nhân sự Employee list
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    status:
                      type: string
                      default: working
                  required:
                  - status
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 50
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code
                  - name
                  - email
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code_sort
                status:
                  type: string
                  default: working
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - status
            example:
              query:
                status: working
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 50
              searchFields:
              - hr_code
              - name
              - email
              sort:
              - hr_code_sort
              status: working
  /shift/list:
    post:
      summary: <PERSON>h sách ca làm việc Shift list
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties: {}
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 20
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - name
                sort:
                  type: array
                  items:
                    type: string
                  default: []
                populates:
                  type: array
                  items:
                    type: string
                  default: []
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
            example:
              query: {}
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 20
              searchFields:
              - name
              sort: []
              populates: []
  /attendance-assign/list:
    post:
      summary: Danh sách phân ca Attendance assign list
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    type:
                      type: string
                      default: department
                  required:
                  - type
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 20
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - name
                  - code
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - -updatedAt
                populates:
                  type: array
                  items:
                    type: string
                  default: []
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
            example:
              query:
                type: department
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 20
              searchFields:
              - name
              - code
              sort:
              - -updatedAt
              populates: []
  /checkin-log/find:
    post:
      summary: Dữ liệu chấm công (chi tiết) checkin log
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    employee_id:
                      type: integer
                      default: 3
                    company_id:
                      type: integer
                      default: 1
                    type:
                      type: object
                      properties:
                        $in:
                          type: array
                          items:
                            type: string
                          default:
                          - checkin
                          - checkout
                          - import
                      required:
                      - $in
                    datetime:
                      type: object
                      properties:
                        $gte:
                          type: string
                          format: date-time
                          default: '2025-03-03T17:00:00.000Z'
                        $lte:
                          type: string
                          format: date-time
                          default: '2025-03-04T16:59:59.999Z'
                      required:
                      - $gte
                      - $lte
                  required:
                  - employee_id
                  - company_id
                  - type
                  - datetime
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - datetime
                addCompanyUtcOffsetToQueryDatetime:
                  type: integer
                  default: true
              required:
              - query
              - sort
              - addCompanyUtcOffsetToQueryDatetime
            example:
              query:
                employee_id: 3
                company_id: 1
                type:
                  $in:
                  - checkin
                  - checkout
                  - import
                datetime:
                  $gte: '2025-03-03T17:00:00.000Z'
                  $lte: '2025-03-04T16:59:59.999Z'
              sort:
              - datetime
              addCompanyUtcOffsetToQueryDatetime: true
  /employee/list-with-timesheet:
    post:
      summary: Bảng công Employee list timesheet
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: object
                  properties:
                    status:
                      type: string
                      default: working
                  required:
                  - status
                search:
                  type: string
                  default: ''
                page:
                  type: integer
                  default: 1
                total:
                  type: integer
                  default: 0
                totalPages:
                  type: integer
                  default: 0
                pageSize:
                  type: integer
                  default: 10
                searchFields:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code
                  - name
                  - uid
                  - email
                  - phone
                sort:
                  type: array
                  items:
                    type: string
                  default:
                  - hr_code_sort
                populates:
                  type: array
                  items:
                    type: string
                  default: []
                month:
                  type: integer
                  default: 2
                year:
                  type: integer
                  default: 2025
              required:
              - query
              - search
              - page
              - total
              - totalPages
              - pageSize
              - searchFields
              - sort
              - populates
              - month
              - year
            example:
              query:
                status: working
              search: ''
              page: 1
              total: 0
              totalPages: 0
              pageSize: 10
              searchFields:
              - hr_code
              - name
              - uid
              - email
              - phone
              sort:
              - hr_code_sort
              populates: []
              month: 2
              year: 2025
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
- bearerAuth: []
