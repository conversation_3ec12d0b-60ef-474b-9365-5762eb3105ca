LLM_TYPE=chatgpt
API_BASE_URL=https://api.weekly.vn
MAX_CONTEXT_LENGTH=5
TIMEOUT=30
DB_URI=postgresql://user:pass@localhost:5432/db
API_TOKEN=
OPENAI_API_KEY=
GEMINI_API_KEY=
OPENROUTER_API_KEY=
GROQ_API_KEY=
VLLM_API_URL=http://**************:8000/v1

# VLLM OWNED MODELS
VLLM_MODEL=hosted_vllm/QwQ-32B

# OPENAI MODELS
OPENAI_MODEL=openai/gpt-4o-mini
#OPENAI_MODEL=openai/gpt-4.1-nano
#OPENAI_MODEL=openai/gpt-4.1-mini

# GEMINI MODELS
MODEL_NAME=gemini/gemini-2.0-flash-exp
#MODEL_NAME=gemini/gemini-2.0-flash-lite

# GROQ MODELS
#MODEL_NAME=groq/llama-3.3-70b-versatile
#MODEL_NAME=groq/meta-llama/llama-4-maverick-17b-128e-instruct
#MODEL_NAME=groq/meta-llama/llama-4-scout-17b-16e-instruct
#MODEL_NAME=groq/llama-3.1-8b-instant

# OPENROUTER MODELS
#MODEL_NAME=openrouter/google/gemini-2.0-flash-exp:free

#MODEL_NAME=openrouter/meta-llama/llama-4-maverick:free
#MODEL_NAME=openrouter/meta-llama/llama-4-scout:free
#MODEL_NAME=openrouter/meta-llama/llama-3.3-70b-instruct:free

#MODEL_NAME=openrouter/qwen/qwen2.5-vl-72b-instruct:free
#MODEL_NAME=openrouter/qwen/qwen-2.5-72b-instruct:free

#MODEL_NAME=openrouter/deepseek/deepseek-chat-v3-0324:free
#MODEL_NAME=openrouter/deepseek/deepseek-chat:free

TEMPERATURE=0
MAX_TOKENS=10000
SYSTEM_PROMPT=You are a friendly chatbot helping employees with internal company information